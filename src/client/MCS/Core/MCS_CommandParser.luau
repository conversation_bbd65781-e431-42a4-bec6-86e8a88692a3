--[[
    CommandParser

    Client-side parsing and validation for commands:
    - Checks if text is a command
    - Parses command structure
    - Basic validation before sending to server
]]

-- Services
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Modules
local Constants = require(ReplicatedStorage.MCS.Shared.MCS_Constants)
local Utils = require(ReplicatedStorage.MCS.Shared.MCS_Utils)

-- Command Parser data store
local CommandParser = {}

-- Configuration
local CONFIG = {
	PREFIX = Constants.Prefix,
	MAX_COMMAND_LENGTH = Constants.MaxCommandLength,
	MAX_ARGS = Constants.MaxArgs,
}

-- Debug logging
local function debugLog(message)
	Utils.print("CommandParser", message)
end

-- Check if text is a command (starts with prefix)
function CommandParser.isCommand(text: string): boolean
	if typeof(text) ~= "string" then
		return false
	end

	return text:match("^%s*" .. CONFIG.PREFIX .. "%S+") ~= nil
end

-- Parse command text into name and arguments
function CommandParser.parse(text: string): (string?, { string }?, string?)
	debugLog("Parsing command: " .. text)
	Utils.startTimer("parse")

	if not CommandParser.isCommand(text) then
		Utils.endTimer("CommandParser", "parse")
		return nil, nil
	end

	-- Remove prefix and trim
	local commandText = text:match("^%s*" .. CONFIG.PREFIX .. "(.+)$")
	if not commandText then
		Utils.endTimer("CommandParser", "parse")
		return nil, nil
	end

	-- Validate command length
	if #commandText > CONFIG.MAX_COMMAND_LENGTH then
		Utils.endTimer("CommandParser", "parse")
		return nil, nil, "Command too long"
	end

	-- Use the Utils module to split the command text into tokens
	local tokens = Utils.splitCommandText(commandText)

	if #tokens == 0 then
		Utils.endTimer("CommandParser", "parse")
		return nil, nil, "Empty command"
	end

	-- First token is the command name
	local commandName = tokens[1]:lower()
	table.remove(tokens, 1)

	-- Limit number of arguments
	if #tokens > CONFIG.MAX_ARGS then
		tokens = { unpack(tokens, 1, CONFIG.MAX_ARGS) }
	end

	Utils.endTimer("CommandParser", "parse")
	return commandName, tokens
end

-- Validate command structure before sending to server
function CommandParser.validate(commandName: string, args: { string }?): (boolean, string?)
	debugLog("Validating command: " .. commandName)

	if not commandName or #commandName == 0 then
		return false, "Invalid command name"
	end

	if #commandName > 50 then
		return false, "Command name too long"
	end

	if args and #args > CONFIG.MAX_ARGS then
		return false, "Too many arguments"
	end

	debugLog("Command passed validation")
	return true, nil
end

-- Format a command string (useful for suggestions)
function CommandParser.format(commandName: string, args: { string }?): string
	debugLog("Command parser formatting now")
	local text = CONFIG.PREFIX .. commandName

	if args then
		for _, arg in ipairs(args) do
			-- Add quotes if argument contains spaces
			if arg:find("%s") then
				text = text .. ' "' .. arg .. '"'
			else
				text = text .. " " .. arg
			end
		end
	end

	return text
end

debugLog("Module initialized")
return CommandParser