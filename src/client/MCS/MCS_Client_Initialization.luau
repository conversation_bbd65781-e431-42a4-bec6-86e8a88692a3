--[[
    MCS: Roblox Modular Command System
    Client Entry Point

    This script initializes the client-side components of the MCS.
    It sets up UI, command parsing, and remote event handling.
]]

-- Services
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TextChatService = game:GetService("TextChatService")
local UserInputService = game:GetService("UserInputService")

-- Player references
local remotes = ReplicatedStorage:WaitForChild("MCS"):WaitForChild("Remotes")
local commandRemote = remotes:WaitForChild("CommandRemote")
local autocompleteRemote = remotes:WaitForChild("AutocompleteRemote")

-- Load client-side components
local AutocompleteService = require(script.Parent.Core.MCS_AutocompleteService)
local CommandParser = require(script.Parent.Core.MCS_CommandParser)

-- Set up default UI components
local Console = require(script.Parent.UI.MCS_Console_UI)
local FeedbackDisplay = require(script.Parent.UI.MCS_Feedback)

-- Initialize UI components
Console.init()
FeedbackDisplay.init()

-- Set up command input handling from various sources
local function handleCommandInput(text)
	-- Debug message
	print("MCS Handle Command Input Initialized")

	-- Check if this is a command
	if not CommandParser.isCommand(text) then
		return
	end

	-- Parse the command to get the name and arguments
	local commandName, args = CommandParser.parse(text)

	-- Validate the command structure
	local isValid, errorMessage = CommandParser.validate(commandName, args)

	if not isValid then
		-- Display feedback to user without sending to server
		FeedbackDisplay.showFeedback(false, errorMessage or "Invalid command format")

		-- Auto-hide feedback after a few seconds
		task.delay(5, function()
			FeedbackDisplay.hideFeedback()
		end)
		return
	end

	-- Send validated command to server
	commandRemote:FireServer(text)
end

-- Initialize AutocompleteService (just once)
AutocompleteService.init(autocompleteRemote)

-- Force first update of command suggestions
task.spawn(function()
	AutocompleteService.getCommandSuggestions("")
end)

-- Set up TextChatService integration (for newer chat system)
if TextChatService and TextChatService.ChatVersion == Enum.ChatVersion.TextChatService then
	local function onIncomingMessage(message)
		if CommandParser.isCommand(message.Text) then
			-- Cancel the chat message from appearing in chat
			message.CancellationToken:Cancel()

			-- Process the command
			handleCommandInput(message.Text)
			return
		end
	end

	-- Connect to main channel
	local channel = TextChatService.TextChannels:FindFirstChild("RBXGeneral")
	if channel then
		channel.MessageReceived:Connect(onIncomingMessage)
	end
end

-- Set up custom Console keybind (default: "F2")
local function onInputBegan(input, gameProcessed)
	-- If game has processed the input, stop
	if gameProcessed then
		return
	end

	if input.KeyCode == Enum.KeyCode.F2 then
		-- Show Console
		Console.show()
	end
end

UserInputService.InputBegan:Connect(onInputBegan)

-- Handle command feedback from server
commandRemote.OnClientEvent:Connect(function(success, message)
	FeedbackDisplay.showFeedback(success, message)

	-- Auto-hide feedback after a few seconds
	task.delay(5, function()
		FeedbackDisplay.hideFeedback()
	end)
end)

-- Debug message
print("MCS Client initialized")
