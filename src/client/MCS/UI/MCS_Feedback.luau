--[[
    FeedbackDisplay Module
    Provides feedback notification functionality for MCS

    Modern terminal-inspired UI with consistent styling
    Dynamic sizing and improved animations
]]

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TextService = game:GetService("TextService")
local TweenService = game:GetService("TweenService")

-- Player references
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Feedback Display Data storage
local FeedbackDisplay = {}

-- Module
local UI_THEME = require(ReplicatedStorage.MCS.Shared.UI_Constants)

-- Variables
local feedbackLabel
local backgroundFrame
local statusIndicator
local currentAnimation -- Track current animation tween

-- Animation constants
local ANIMATION = {
	FADE_IN_TIME = 0.4, -- Slightly longer for smoother appearance
	DISPLAY_TIME = 4,
	FADE_OUT_TIME = 0.6, -- Slightly longer for smoother disappearance
	SCALE_TIME = 0.35, -- Time for scaling animations
	MIN_WIDTH = 250, -- Minimum width in pixels
	MAX_WIDTH = 600, -- Maximum width in pixels
	HEIGHT = 40, -- Base height
	PADDING_X = 20, -- Horizontal padding
	PADDING_Y = 10, -- Vertical padding
}

-- Initialization of FeedbackDisplay
function FeedbackDisplay.init()
	-- Print Debug Message
	print("MCS Feedback Display Initialized")
	-- Reuse the ScreenGui from Console if it exists, or create a new one
	local gui = playerGui:FindFirstChild("MCS_Console") or Instance.new("ScreenGui")
	if not gui.Parent then
		gui.Name = "MCS_Console"
		gui.ResetOnSpawn = false
		gui.Parent = playerGui
	end

	-- Create container frame with background
	backgroundFrame = Instance.new("Frame")
	backgroundFrame.Name = "FeedbackContainer"
	backgroundFrame.Size = UDim2.new(0, 500, 0, ANIMATION.HEIGHT)
	backgroundFrame.Position = UDim2.new(0.5, -250, 0.05, 0)
	backgroundFrame.BackgroundColor3 = UI_THEME.BACKGROUND
	backgroundFrame.BorderColor3 = UI_THEME.BORDER
	backgroundFrame.BorderSizePixel = 1
	backgroundFrame.AnchorPoint = Vector2.new(0.5, 0) -- Center anchoring for better animation

	-- Add rounded corners
	local uiCorner = Instance.new("UICorner")
	uiCorner.CornerRadius = UI_THEME.CORNER_RADIUS
	uiCorner.Parent = backgroundFrame

	-- Add drop shadow
	local uiStroke = Instance.new("UIStroke")
	uiStroke.Color = UI_THEME.BORDER
	uiStroke.Transparency = 0.7
	uiStroke.Thickness = 1
	uiStroke.Parent = backgroundFrame

	-- Create TextLabel for feedback with text wrapping enabled
	feedbackLabel = Instance.new("TextLabel")
	feedbackLabel.Name = "Feedback"
	feedbackLabel.Size = UDim2.new(1, -ANIMATION.PADDING_X, 1, 0)
	feedbackLabel.Position = UDim2.new(0, 10, 0, 0)
	feedbackLabel.BackgroundTransparency = 1
	feedbackLabel.TextColor3 = UI_THEME.TEXT
	feedbackLabel.TextSize = 16
	feedbackLabel.TextXAlignment = Enum.TextXAlignment.Left
	feedbackLabel.TextYAlignment = Enum.TextYAlignment.Center
	feedbackLabel.TextWrapped = true -- Enable text wrapping
	feedbackLabel.Text = ""
	feedbackLabel.Parent = backgroundFrame

	-- Add status indicator
	statusIndicator = Instance.new("Frame")
	statusIndicator.Name = "StatusIndicator"
	statusIndicator.Size = UDim2.new(0, 4, 1, -10)
	statusIndicator.Position = UDim2.new(0, 3, 0, 5)
	statusIndicator.BackgroundColor3 = UI_THEME.SUCCESS_TEXT
	statusIndicator.BorderSizePixel = 0
	statusIndicator.Visible = false

	local indicatorCorner = Instance.new("UICorner")
	indicatorCorner.CornerRadius = UDim.new(1, 0)
	indicatorCorner.Parent = statusIndicator

	statusIndicator.Parent = backgroundFrame

	-- Set initial properties
	backgroundFrame.Transparency = 1
	backgroundFrame.Visible = false
	backgroundFrame.Parent = gui
end

-- Calculate optimal size based on text content
function FeedbackDisplay.calculateOptimalSize(message)
	-- Use TextService to get text bounds
	local textParams = Instance.new("GetTextBoundsParams")
	textParams.Text = message
	textParams.Size = 16
	textParams.Width = ANIMATION.MAX_WIDTH - ANIMATION.PADDING_X

	local textBounds = TextService:GetTextBoundsAsync(textParams)

	-- Calculate width and height with padding
	local width =
		math.clamp(textBounds.X + ANIMATION.PADDING_X, ANIMATION.MIN_WIDTH, ANIMATION.MAX_WIDTH)

	-- Determine if we need multiple lines
	local height = ANIMATION.HEIGHT
	if textBounds.X > (ANIMATION.MAX_WIDTH - ANIMATION.PADDING_X) then
		-- If text is wider than max width, it will wrap and need more height
		height = math.max(ANIMATION.HEIGHT, textBounds.Y + ANIMATION.PADDING_Y)
	end

	return UDim2.new(0, width, 0, height)
end

-- Cancel existing animations
function FeedbackDisplay.cancelAnimations()
	if currentAnimation then
		currentAnimation:Cancel()
		currentAnimation = nil
	end
end

-- Show Feedback with enhanced animations
function FeedbackDisplay.showFeedback(success, message)
	-- Cancel any running animations
	FeedbackDisplay.cancelAnimations()

	-- Configure status indicator
	if statusIndicator then
		statusIndicator.BackgroundColor3 = success and UI_THEME.SUCCESS_TEXT or UI_THEME.ERROR_TEXT
		statusIndicator.Visible = true
	end

	-- Update text and color
	feedbackLabel.Text = message
	feedbackLabel.TextColor3 = success and UI_THEME.SUCCESS_TEXT or UI_THEME.ERROR_TEXT

	-- Calculate optimal size based on text content
	local targetSize = FeedbackDisplay.calculateOptimalSize(message)

	-- Update position based on new size to keep centered
	local newXOffset = -targetSize.X.Offset / 2

	-- Set up initial state for animation
	backgroundFrame.Size = UDim2.new(0, 0, 0, targetSize.Y.Offset) -- Start with zero width
	backgroundFrame.Position = UDim2.new(0.5, 0, 0.05, 0) -- Start centered
	backgroundFrame.Transparency = 0.5
	backgroundFrame.Visible = true

	-- Create and play entrance animation sequence
	local expandTween = TweenService:Create(
		backgroundFrame,
		TweenInfo.new(ANIMATION.SCALE_TIME, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
		{
			Size = targetSize,
			Position = UDim2.new(0.5, newXOffset, 0.05, 0),
		}
	)

	local fadeTween = TweenService:Create(
		backgroundFrame,
		TweenInfo.new(ANIMATION.FADE_IN_TIME, Enum.EasingStyle.Sine, Enum.EasingDirection.Out),
		{ Transparency = 0 }
	)

	-- Play expand animation first, then fade in
	currentAnimation = expandTween
	expandTween:Play()

	expandTween.Completed:Connect(function()
		currentAnimation = fadeTween
		fadeTween:Play()
	end)

	-- Schedule auto-hide after display time
	task.delay(ANIMATION.DISPLAY_TIME, function()
		FeedbackDisplay.hideFeedback()
	end)
end

-- Hide Feedback with enhanced animations
function FeedbackDisplay.hideFeedback()
	-- Only hide if currently visible
	if not backgroundFrame.Visible then
		return
	end

	-- Cancel any running animations
	FeedbackDisplay.cancelAnimations()

	-- Get current size for animation
	local currentSize = backgroundFrame.Size
	local currentPosition = backgroundFrame.Position

	-- Create and play fade-out and shrink animation sequence
	local fadeTween = TweenService:Create(
		backgroundFrame,
		TweenInfo.new(ANIMATION.FADE_OUT_TIME, Enum.EasingStyle.Sine, Enum.EasingDirection.In),
		{ Transparency = 1 }
	)

	local shrinkTween = TweenService:Create(
		backgroundFrame,
		TweenInfo.new(ANIMATION.SCALE_TIME, Enum.EasingStyle.Back, Enum.EasingDirection.In),
		{
			Size = UDim2.new(0, 0, 0, currentSize.Y.Offset),
			Position = UDim2.new(0.5, 0, 0.05, 0),
		}
	)

	-- Sequence: fade out first, then shrink
	currentAnimation = fadeTween
	fadeTween:Play()

	fadeTween.Completed:Connect(function()
		currentAnimation = shrinkTween
		shrinkTween:Play()

		shrinkTween.Completed:Connect(function()
			backgroundFrame.Visible = false
		end)
	end)
end

return FeedbackDisplay
