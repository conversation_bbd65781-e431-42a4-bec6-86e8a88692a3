-- soon: !strict

--[[
    TBRDS_Client.client.luau

	- Client Tag Handler
	- This LocalScript handles the client-side display of tags
	above the player heads

    *Dynamic Innovative Studio*
]]

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Player Reference
local LocalPlayer = Players.LocalPlayer

-- Modules
local Utils = require(ReplicatedStorage.TBRDS.TBRDS_Types)

-- Remotes
local TBRDS = ReplicatedStorage:WaitForChild("TBRDS")
local Remotes = TBRDS:WaitForChild("Remotes")
local TagRemote = Remotes:WaitForChild("TagRemote")
local TagRequestRemote = Remotes:WaitForChild("TagRequestRemote")

-- Debug logging
local function debugLog(message)
	Utils.print("Client", message)
end

-- Handle tag updates from server
TagRemote.OnClientEvent:Connect(function(player, tag, style)
	if player == LocalPlayer then
		-- This is handled by the server
		return
	end

	-- This is now just for verification/debugging
	debugLog(string.format("Received tag update for %s: %s", player.Name, tag))
end)

-- Request our tag when character loads
LocalPlayer.CharacterAdded:Connect(function()
	TagRequestRemote:FireServer()
	debugLog("Requested tag from server")
end)

-- Request tag when we join
if LocalPlayer.Character then
	TagRequestRemote:FireServer()
	debugLog("Requested tag from server (initial)")
end
