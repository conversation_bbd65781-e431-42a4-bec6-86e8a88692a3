--!strict

--[[
    - file: MAFS_Client_Initilization.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Client-side initialization script for the Modular Audio FootStep System (MAFS)
      - Loads the MAFS Client Module
      - Demonstrates the usage of the public API
]]

-- ============================================================================
-- REFERENCES
-- ============================================================================
local FootStepClientModule = script.Parent:FindFirstChild("MAFS_Client_Module")

-- Check if the module exists
if not FootStepClientModule then
  warn("MAFS: FootStepClientModule module not found in expected location")
  return
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

-- Initialize the client-side MAFS system
local footstepSystem = require(script.Parent:WaitForChild("MAFS_Client_Module"))
local success = footstepSystem.Initialize()

-- Handle initialization result
if success then
  print("MAFS: Client-side MAFS system initialized successfully")
else
  warn("MAFS: Failed to initialize client-side MAFS system")
end

--[[
    PUBLIC API EXAMPLES

    The footstep system provides the following public APIs that can be used
    by other client scripts to interact with the system:

    -- Set the volume of footsteps (0-1)
    footstepSystem.SetVolume(0.8)

    -- Disable footsteps temporarily (useful for cutscenes)
    footstepSystem.SetEnabled(false)

    -- Re-enable footsteps after disabling
    footstepSystem.SetEnabled(true)
]]
