--!strict

--[[
 - file: MAFS_CLIENT_MODULE.LUAU

 - version: 2.0.0
 - author: BleckWolf25
 - contributors:

 - copyright: Dynamic Innovative Studio

 - description:
   - Client-side module for the Modular Audio FootStep System (MAFS).
   - Handles real-time footstep detection, sound playback, and client-server communication.
   - Implements object pooling for sound optimization and 3D spatial audio positioning.
   - Provides movement threshold detection and server validation for anti-exploit protection.
   - Integrates with MAFS configuration system for material-based sound mapping.
   - Should be required by the client-side initialization script.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local SoundService = game:GetService("SoundService")

-- ============================================================================
-- MODULES
-- ============================================================================

local MAFSConfig =
  require(ReplicatedStorage.Configurations.Systems:WaitForChild("MAFS_Configuration"))
local MAFSRemotes = require(ReplicatedStorage:WaitForChild("MAFS"):WaitForChild("MAFS_Remotes"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================

local STEP_INTERVAL = MAFSConfig.Settings.StepInterval
local MOVEMENT_THRESHOLD = MAFSConfig.Settings.MovementThreshold
local CLIENT_COOLDOWN = MAFSConfig.Settings.ClientCooldown

-- Sound system constants
local MAX_CACHE_SIZE = MAFSConfig.Settings.MaxCachedSounds

-- ============================================================================
-- MODULE REFERENCES
-- ============================================================================

local LocalPlayer = Players.LocalPlayer
local FootstepEvent = MAFSRemotes.GetFootstepEvent()

-- ============================================================================
-- TYPE ANNOTATIONS & INFER USAGE
-- ============================================================================

-- Audio configuration data structure for footstep sounds
export type AudioData = {
  SoundId: string, -- Roblox asset ID for the sound
  Volume: number, -- Sound volume (0.0-2.0)
  PlaybackSpeed: number, -- Playback speed multiplier
  Position: Vector3, -- 3D world position for spatial audio
  RollOffMinDistance: number?, -- Distance where volume starts decreasing
  RollOffMaxDistance: number?, -- Distance where sound becomes inaudible
}

-- Sound cache structure for object pooling optimization
type SoundCache = { [string]: { Sound } }

-- Module interface for external usage
export type FootstepClientModule = {
  GetSoundFromCache: (soundId: string) -> Sound,
  PlaySound: (audioData: AudioData) -> Sound?,
  IsMoving: () -> boolean,
  RequestFootstep: () -> (),
  HandleFootstepAudio: (audioData: AudioData) -> (),
  SetupCharacter: (newCharacter: Model?) -> (),
  CleanupCharacter: () -> (),
  SetVolume: (volume: number) -> (),
  SetEnabled: (enabled: boolean) -> (),
  Initialize: () -> boolean,
}

-- ============================================================================
-- MODULE DEFINITION
-- ============================================================================

local FootstepClient = {}

-- ============================================================================
-- MODULE STATE
-- ============================================================================

local character: Model? = nil
local humanoid: Humanoid? = nil
local lastPosition = Vector3.new(0, 0, 0)
local lastFootstepTime = 0
local footstepEnabled = true

-- ============================================================================
-- SOUND SYSTEM
-- ============================================================================

-- Sound optimization: Pre-create and reuse sound objects using object pooling
-- This reduces garbage collection and improves performance for frequent sound playback
local soundCache: SoundCache = {}

-- Create a footstep sound group for centralized volume control and mixing
local footstepSoundGroup = Instance.new("SoundGroup")
footstepSoundGroup.Name = "FootstepSoundGroup"
footstepSoundGroup.Volume = 1
footstepSoundGroup.Parent = SoundService

-- ============================================================================
-- SOUND OPERATIONS
-- ============================================================================

--[[
    Gets or creates a sound object from the cache

    @param soundId (string) - The asset ID of the sound to play
    @return (Sound) - A configured Sound instance ready for playback
]]
function FootstepClient.GetSoundFromCache(soundId: string): Sound
  -- Initialize cache entry for this sound if it doesn't exist
  if not soundCache[soundId] then
    soundCache[soundId] = {}
  end

  local soundList = soundCache[soundId]

  -- Try to find an unused sound in the cache
  for i = #soundList, 1, -1 do
    local sound = soundList[i]
    if not sound.Playing then
      table.remove(soundList, i)
      table.insert(soundList, sound) -- Move to end
      return sound
    end
  end

  -- If we didn't find one and cache isn't full, create a new one
  if #soundList < MAX_CACHE_SIZE then
    local newSound = Instance.new("Sound")
    newSound.SoundId = soundId
    newSound.SoundGroup = footstepSoundGroup
    newSound.Parent = workspace
    table.insert(soundList, newSound)
    return newSound
  end

  -- If cache is full, reuse the oldest sound (FIFO replacement strategy)
  local oldestSound = soundList[1]
  table.remove(soundList, 1)
  table.insert(soundList, oldestSound)
  return oldestSound
end

--[[
    Plays a footstep sound with the provided audio data

    @param audioData (table) - Contains sound configuration:
        - SoundId: Asset ID for the sound
        - Volume: Sound volume (0-1)
        - PlaybackSpeed: Playback rate modifier
        - Position: 3D world position for the sound
    @return (Sound) - The sound object that was played
]]
function FootstepClient.PlaySound(audioData: AudioData): Sound?
  local success, result = pcall(function()
    -- Get a sound object from the cache
    local sound = FootstepClient.GetSoundFromCache(audioData.SoundId)

    -- Configure the sound
    sound.SoundId = audioData.SoundId
    sound.Volume = audioData.Volume
    sound.PlaybackSpeed = audioData.PlaybackSpeed

    -- Set 3D position by parenting to a part at the position
    local soundPart: Part = Instance.new("Part")
    soundPart.Name = "FootstepSoundPart"
    soundPart.Anchored = true
    soundPart.CanCollide = false
    soundPart.Transparency = 1
    soundPart.Size = Vector3.new(0.1, 0.1, 0.1)
    soundPart.Position = audioData.Position
    soundPart.Parent = workspace

    sound.Parent = soundPart
    sound.RollOffMode = Enum.RollOffMode.Linear
    sound.RollOffMinDistance = audioData.RollOffMinDistance or 5
    sound.RollOffMaxDistance = audioData.RollOffMaxDistance or 50

    -- Play the sound
    sound:Play()

    -- Clean up the part after the sound finishes
    sound.Ended:Connect(function()
      local part = sound.Parent
      if part and part:IsA("Part") and part.Parent then
        part:Destroy()
      end
    end)

    -- Debug logging
    if MAFSConfig.IsDebugMode() then
      print(
        string.format(
          "MAFS: Playing sound %s at %s",
          audioData.SoundId,
          tostring(audioData.Position)
        )
      )
    end

    return sound
  end)

  if not success then
    warn("MAFS: Failed to play sound:", result)
    return nil
  end

  return result :: Sound
end

-- ============================================================================
-- EVENT FUNCTIONS
-- ============================================================================

--[[
    Determines if the player is currently moving on the ground

    @return (boolean) - True if player is moving above threshold, false otherwise
]]
function FootstepClient.IsMoving(): boolean
  if not character or not humanoid then
    return false
  end

  -- Type guard to ensure humanoid is not nil
  local currentHumanoid = humanoid :: Humanoid

  -- Check if the player is on the ground (not jumping or falling)
  if currentHumanoid.FloorMaterial == Enum.Material.Air then
    return false
  end

  -- Check the player's speed
  local rootPart = character:FindFirstChild("HumanoidRootPart") :: Part?
  if not rootPart then
    return false
  end

  -- Calculate displacement and speed
  local currentPosition = rootPart.Position
  local displacement = (currentPosition - lastPosition).Magnitude
  local speed = displacement / STEP_INTERVAL

  lastPosition = currentPosition
  local state = currentHumanoid:GetState()

  -- Return true if moving above threshold and not swimming
  return speed > MOVEMENT_THRESHOLD
    and (
      state == Enum.HumanoidStateType.Running or state == Enum.HumanoidStateType.RunningNoPhysics
    )
end

--[[
    Requests a footstep sound from the server if conditions are met
    This is called on each RenderStep when footsteps are enabled
]]
function FootstepClient.RequestFootstep(): ()
  if not character then
    return
  end

  local rootPart = character:FindFirstChild("HumanoidRootPart") :: Part?
  if not rootPart then
    return
  end

  local currentTime = tick()

  -- Check cooldown
  if currentTime - lastFootstepTime < CLIENT_COOLDOWN then
    return
  end

  -- Check if player is moving
  if FootstepClient.IsMoving() then
    lastFootstepTime = currentTime

    -- Send footstep request to server for validation
    local currentFootstepEvent = FootstepEvent
    if currentFootstepEvent then
      currentFootstepEvent:FireServer(rootPart.Position)
    else
      warn("MAFS: FootstepEvent not available")
    end
  end
end

--[[
    Handles incoming footstep audio data from the server

    @param audioData (table) - Sound configuration data
]]
function FootstepClient.HandleFootstepAudio(audioData: AudioData): ()
  -- Play the footstep sound
  FootstepClient.PlaySound(audioData)
end

-- ============================================================================
-- CHARACTER OPERATIONS
-- ============================================================================

--[[
    Sets up tracking for a new character

    @param newCharacter (Model) - The player's character model
]]
function FootstepClient.SetupCharacter(newCharacter: Model?): ()
  -- Clean up previous connections
  FootstepClient.CleanupCharacter()

  -- Set up new character
  character = newCharacter
  if not character then
    return
  end

  -- Wait for humanoid
  humanoid = character:WaitForChild("Humanoid") :: Humanoid

  -- Reset state
  local rootPart = character:FindFirstChild("HumanoidRootPart") :: Part?
  lastPosition = if rootPart then rootPart.Position else Vector3.new(0, 0, 0)
  lastFootstepTime = 0
end

--[[
    Cleans up the current character connections and references
]]
function FootstepClient.CleanupCharacter(): ()
  character = nil
  humanoid = nil
end

-- ============================================================================
-- VOLUME OPERATIONS
-- ============================================================================

--[[
    Sets the volume for all footstep sounds

    @param volume (number) - Volume level (0-1)
]]
function FootstepClient.SetVolume(volume: number): ()
  footstepSoundGroup.Volume = math.clamp(volume, 0, 1)
end

--[[
    Enables or disables footstep sound playback

    @param enabled (boolean) - Whether footsteps should be played
]]
function FootstepClient.SetEnabled(enabled: boolean): ()
  footstepEnabled = enabled
end

-- ============================================================================
-- INITIALIZATION FUNCTION
-- ============================================================================

--[[
    Initializes the client-side footstep system
    - Sets up character tracking
    - Connects to server events
    - Starts the movement detection loop
]]
function FootstepClient.Initialize(): boolean
  -- Initialize remotes first
  if not MAFSRemotes.Initialize() then
    warn("MAFS: Failed to initialize remotes, system cannot start")
    return false
  end

  -- Get the footstep event
  local footstepEventResult = MAFSRemotes.GetFootstepEvent()
  if not footstepEventResult then
    warn("MAFS: Failed to get footstep event")
    return false
  end
  FootstepEvent = footstepEventResult

  -- Wait for character if needed
  local currentLocalPlayer = LocalPlayer :: Player
  local localPlayerCharacter = currentLocalPlayer.Character
  if not localPlayerCharacter then
    currentLocalPlayer.CharacterAdded:Wait()
    localPlayerCharacter = currentLocalPlayer.Character
  end

  -- Set up character tracking
  FootstepClient.SetupCharacter(localPlayerCharacter)
  currentLocalPlayer.CharacterAdded:Connect(FootstepClient.SetupCharacter)

  -- Set up event handling
  local currentFootstepEvent = FootstepEvent
  if currentFootstepEvent then
    currentFootstepEvent.OnClientEvent:Connect(FootstepClient.HandleFootstepAudio)
  end

  -- Set up heartbeat for movement detection using RenderStep for smooth timing
  RunService:BindToRenderStep(
    "FootstepCheck",
    Enum.RenderPriority.Camera.Value + 1, -- Priority just after camera updates
    function()
      if footstepEnabled and character then
        FootstepClient.RequestFootstep()
      end
    end
  )

  -- Preload sounds from configuration
  for _, materialData in pairs(MAFSConfig.MaterialSounds) do
    for _, soundId in materialData.SoundIds do
      FootstepClient.GetSoundFromCache(soundId :: string)
    end
  end

  for _, materialData in pairs(MAFSConfig.CustomMaterials) do
    for _, soundId in materialData.SoundIds do
      FootstepClient.GetSoundFromCache(soundId :: string)
    end
  end

  print("MAFS: FootstepClient initialized successfully")
  return true
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return FootstepClient :: FootstepClientModule
