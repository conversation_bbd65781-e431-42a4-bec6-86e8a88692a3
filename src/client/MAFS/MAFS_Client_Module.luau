--!strict

--[[
    - file: MAFS_Client_Module.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Client-side module for the Modular Audio FootStep System (MAFS)
      - Handles footstep detection, sound playback, and client-server communication
      - Should be required by the client-side initialization script
]]

-- ============================================================================
-- MODULE DEFINITION
-- ============================================================================
local FootstepClient = {}

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local SoundService = game:GetService("SoundService")

-- ============================================================================
-- MODULE IMPORTS
-- ============================================================================
local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS_Configuration)
local MAFSRemotes = require(ReplicatedStorage.MAFS.MAFS_Remotes)

-- ============================================================================
-- MODULE REFERENCES
-- ============================================================================
local LocalPlayer = Players.LocalPlayer
local FootstepEvent = MAFSRemotes.GetFootstepEvent()

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local STEP_INTERVAL = MAFSConfig.Settings.StepInterval
local MOVEMENT_THRESHOLD = MAFSConfig.Settings.MovementThreshold
local CLIENT_COOLDOWN = MAFSConfig.Settings.ClientCooldown

-- ============================================================================
-- MODULE STATE
-- ============================================================================
local character = nil
local humanoid = nil
local lastPosition = Vector3.new(0, 0, 0)
local lastFootstepTime = 0
local footstepEnabled = true

-- ============================================================================
-- SOUND SYSTEM
-- ============================================================================

-- Sound optimization: Pre-create and reuse sound objects using object pooling
local soundCache = {}
local MAX_CACHE_SIZE = MAFSConfig.Settings.MaxCachedSounds

-- Create a footstep sound group for mixing control
local footstepSoundGroup = Instance.new("SoundGroup")
footstepSoundGroup.Name = "FootstepSoundGroup"
footstepSoundGroup.Volume = 1
footstepSoundGroup.Parent = SoundService

--[[
    Gets or creates a sound object from the cache

    @param soundId (string) - The asset ID of the sound to play
    @return (Sound) - A configured Sound instance ready for playback
]]
function FootstepClient.GetSoundFromCache(soundId)
  -- Initialize cache entry for this sound if it doesn't exist
  if not soundCache[soundId] then
    soundCache[soundId] = {}
  end

  -- Try to find an unused sound in the cache
  for i = #soundCache[soundId], 1, -1 do
    local sound = soundCache[soundId][i]
    if not sound.Playing then
      table.remove(soundCache[soundId], i)
      table.insert(soundCache[soundId], sound) -- Move to end
      return sound
    end
  end

  -- If we didn't find one and cache isn't full, create a new one
  if #soundCache[soundId] < MAX_CACHE_SIZE then
    local newSound = Instance.new("Sound")
    newSound.SoundId = soundId
    newSound.SoundGroup = footstepSoundGroup
    newSound.Parent = workspace
    table.insert(soundCache[soundId], newSound)
    return newSound
  end

  -- If cache is full, reuse the oldest sound (FIFO replacement strategy)
  local oldestSound = soundCache[soundId][1]
  table.remove(soundCache[soundId], 1)
  table.insert(soundCache[soundId], oldestSound)
  return oldestSound
end

--[[
    Plays a footstep sound with the provided audio data

    @param audioData (table) - Contains sound configuration:
        - SoundId: Asset ID for the sound
        - Volume: Sound volume (0-1)
        - PlaybackSpeed: Playback rate modifier
        - Position: 3D world position for the sound
    @return (Sound) - The sound object that was played
]]
function FootstepClient.PlaySound(audioData)
  local success, result = pcall(function()
    -- Get a sound object from the cache
    local sound = FootstepClient.GetSoundFromCache(audioData.SoundId)

    -- Configure the sound
    sound.SoundId = audioData.SoundId
    sound.Volume = audioData.Volume
    sound.PlaybackSpeed = audioData.PlaybackSpeed
    sound.Position = audioData.Position
    sound.RollOffMode = Enum.RollOffMode.Linear
    sound.RollOffMinDistance = audioData.RollOffMinDistance or 5
    sound.RollOffMaxDistance = audioData.RollOffMaxDistance or 50
    sound.EmitterSize = 5 -- Size of the sound emitter in studs

    -- Play the sound
    sound:Play()

    -- Debug logging
    if MAFSConfig.IsDebugMode() then
      print(
        string.format(
          "MAFS: Playing sound %s at %s",
          audioData.SoundId,
          tostring(audioData.Position)
        )
      )
    end

    return sound
  end)

  if not success then
    warn("MAFS: Failed to play sound:", result)
    return nil
  end

  return result
end

--[[
    Determines if the player is currently moving on the ground

    @return (boolean) - True if player is moving above threshold, false otherwise
]]
function FootstepClient.IsMoving()
  if not character or not humanoid then
    return false
  end

  -- Check if the player is on the ground (not jumping or falling)
  if not humanoid.FloorMaterial then
    return false
  end

  -- Check the player's speed
  local rootPart = character:FindFirstChild("HumanoidRootPart")
  if not rootPart then
    return false
  end

  -- Calculate displacement and speed
  local currentPosition = rootPart.Position
  local displacement = (currentPosition - lastPosition).Magnitude
  local speed = displacement / STEP_INTERVAL

  lastPosition = currentPosition
  local state = humanoid:GetState()

  -- Return true if moving above threshold and not swimming
  return speed > MOVEMENT_THRESHOLD
    and (
      state == Enum.HumanoidStateType.Running or state == Enum.HumanoidStateType.RunningNoPhysics
    )
end

--[[
    Requests a footstep sound from the server if conditions are met
    This is called on each RenderStep when footsteps are enabled
]]
function FootstepClient.RequestFootstep()
  if not character then
    return
  end

  local rootPart = character:FindFirstChild("HumanoidRootPart")
  if not rootPart then
    return
  end

  local currentTime = tick()

  -- Check cooldown
  if currentTime - lastFootstepTime < CLIENT_COOLDOWN then
    return
  end

  -- Check if player is moving
  if FootstepClient.IsMoving() then
    lastFootstepTime = currentTime

    -- Send footstep request to server for validation
    if FootstepEvent then
      FootstepEvent:FireServer(rootPart.Position)
    else
      warn("MAFS: FootstepEvent not available")
    end
  end
end

--[[
    Handles incoming footstep audio data from the server

    @param audioData (table) - Sound configuration data
]]
function FootstepClient.HandleFootstepAudio(audioData)
  -- Play the footstep sound
  FootstepClient.PlaySound(audioData)
end

--[[
    Sets up tracking for a new character

    @param newCharacter (Model) - The player's character model
]]
function FootstepClient.SetupCharacter(newCharacter)
  -- Clean up previous connections
  FootstepClient.CleanupCharacter()

  -- Set up new character
  character = newCharacter
  if not character then
    return
  end

  -- Wait for humanoid
  humanoid = character:WaitForChild("Humanoid")

  -- Reset state
  lastPosition = character:FindFirstChild("HumanoidRootPart")
      and character.HumanoidRootPart.Position
    or Vector3.new(0, 0, 0)
  lastFootstepTime = 0
end

--[[
    Cleans up the current character connections and references
]]
function FootstepClient.CleanupCharacter()
  character = nil
  humanoid = nil
end

--[[
    Sets the volume for all footstep sounds

    @param volume (number) - Volume level (0-1)
]]
function FootstepClient.SetVolume(volume)
  footstepSoundGroup.Volume = math.clamp(volume, 0, 1)
end

--[[
    Enables or disables footstep sound playback

    @param enabled (boolean) - Whether footsteps should be played
]]
function FootstepClient.SetEnabled(enabled)
  footstepEnabled = enabled
end

--[[
    Initializes the client-side footstep system
    - Sets up character tracking
    - Connects to server events
    - Starts the movement detection loop
]]
function FootstepClient.Initialize()
  -- Initialize remotes first
  if not MAFSRemotes.Initialize() then
    warn("MAFS: Failed to initialize remotes, system cannot start")
    return false
  end

  -- Get the footstep event
  FootstepEvent = MAFSRemotes.GetFootstepEvent()
  if not FootstepEvent then
    warn("MAFS: Failed to get footstep event")
    return false
  end

  -- Wait for character if needed
  if not LocalPlayer.Character then
    LocalPlayer.CharacterAdded:Wait()
  end

  -- Set up character tracking
  FootstepClient.SetupCharacter(LocalPlayer.Character)
  LocalPlayer.CharacterAdded:Connect(FootstepClient.SetupCharacter)

  -- Set up event handling
  FootstepEvent.OnClientEvent:Connect(FootstepClient.HandleFootstepAudio)

  -- Set up heartbeat for movement detection using RenderStep for smooth timing
  RunService:BindToRenderStep(
    "FootstepCheck",
    Enum.RenderPriority.Camera.Value + 1, -- Priority just after camera updates
    function()
      if footstepEnabled and character then
        FootstepClient.RequestFootstep()
      end
    end
  )

  -- Preload sounds from configuration
  for _, materialData in pairs(MAFSConfig.MaterialSounds) do
    for _, soundId in ipairs(materialData.SoundIds) do
      FootstepClient.GetSoundFromCache(soundId)
    end
  end

  for _, materialData in pairs(MAFSConfig.CustomMaterials) do
    for _, soundId in ipairs(materialData.SoundIds) do
      FootstepClient.GetSoundFromCache(soundId)
    end
  end

  print("MAFS: FootstepClient initialized successfully")
  return true
end

return FootstepClient
