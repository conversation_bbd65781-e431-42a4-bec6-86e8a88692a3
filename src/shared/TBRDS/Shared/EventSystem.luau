-- soon: !strict
--[[
    TBRDS Event System Module

    Centralized event system for tag changes and system events

    ARCHITECTURE ROLE:
    - Provides event-driven architecture for TBRDS
    - Enables other systems to react to tag changes
    - Manages event subscriptions and notifications
    - Supports integration with other game systems

    USAGE:
    - Subscribe to tag events for system integration
    - Fire events when tags change or validation occurs
    - Use for debugging and monitoring

    *Dynamic Innovative Studio*
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Import types and configuration
local TBRDSConfig = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
local Types = require(script.Parent.Types)

type TagEventType = Types.TagEventType
type TagEventData = Types.TagEventData
type EventCallback = Types.EventCallback

local EventSystem = {}

-- Event storage
local eventSubscribers: { [TagEventType]: { [string]: EventCallback } } = {}
local eventHistory: { TagEventData } = {}
local subscriptionCounter = 0

-- Performance tracking
local eventMetrics = {
  eventsProcessed = 0,
  subscribersNotified = 0,
  errorCount = 0,
  lastReset = os.time(),
}

-- Initialize event types
for eventName, _ in pairs(TBRDSConfig.Events) do
  eventSubscribers[eventName] = {}
end

-- Debug logging
local function debugLog(message: string)
  if TBRDSConfig.Settings.DebugMode and TBRDSConfig.Debug.LogValidationEvents then
    print(string.format("[TBRDS:EventSystem]: %s", message))
  end
end

-- Generate unique subscription ID
local function generateSubscriptionId(): string
  subscriptionCounter = subscriptionCounter + 1
  return string.format("sub_%d_%d", subscriptionCounter, os.time())
end

-- Validate event data
local function validateEventData(eventType: TagEventType, eventData: TagEventData): boolean
  if not eventData.Player or not eventData.Player:IsA("Player") then
    return false
  end

  if not eventData.NewRole or type(eventData.NewRole) ~= "string" then
    return false
  end

  if not eventData.Timestamp or type(eventData.Timestamp) ~= "number" then
    return false
  end

  if not eventData.Source or type(eventData.Source) ~= "string" then
    return false
  end

  return true
end

-- Subscribe to an event type
function EventSystem.Subscribe(eventType: TagEventType, callback: EventCallback): string
  if not eventSubscribers[eventType] then
    warn(string.format("TBRDS EventSystem: Unknown event type '%s'", eventType))
    return ""
  end

  if type(callback) ~= "function" then
    warn("TBRDS EventSystem: Callback must be a function")
    return ""
  end

  local subscriptionId = generateSubscriptionId()
  eventSubscribers[eventType][subscriptionId] = callback

  debugLog(string.format("Subscribed to '%s' with ID '%s'", eventType, subscriptionId))
  return subscriptionId
end

-- Unsubscribe from an event
function EventSystem.Unsubscribe(subscriptionId: string): boolean
  for eventType, subscribers in pairs(eventSubscribers) do
    if subscribers[subscriptionId] then
      subscribers[subscriptionId] = nil
      debugLog(string.format("Unsubscribed '%s' from '%s'", subscriptionId, eventType))
      return true
    end
  end

  warn(string.format("TBRDS EventSystem: Subscription ID '%s' not found", subscriptionId))
  return false
end

-- Fire an event to all subscribers
function EventSystem.Fire(eventType: TagEventType, eventData: TagEventData): ()
  if not eventSubscribers[eventType] then
    warn(string.format("TBRDS EventSystem: Unknown event type '%s'", eventType))
    return
  end

  -- Validate event data
  if not validateEventData(eventType, eventData) then
    warn("TBRDS EventSystem: Invalid event data")
    eventMetrics.errorCount = eventMetrics.errorCount + 1
    return
  end

  -- Add to history
  table.insert(eventHistory, eventData)

  -- Limit history size
  if #eventHistory > 100 then
    table.remove(eventHistory, 1)
  end

  -- Notify all subscribers
  local subscriberCount = 0
  for subscriptionId, callback in pairs(eventSubscribers[eventType]) do
    subscriberCount = subscriberCount + 1

    -- Safely call the callback
    local success, err = pcall(callback, eventData)
    if not success then
      warn(
        string.format(
          "TBRDS EventSystem: Error in callback '%s': %s",
          subscriptionId,
          tostring(err)
        )
      )
      eventMetrics.errorCount = eventMetrics.errorCount + 1
    end
  end

  -- Update metrics
  eventMetrics.eventsProcessed = eventMetrics.eventsProcessed + 1
  eventMetrics.subscribersNotified = eventMetrics.subscribersNotified + subscriberCount

  debugLog(string.format("Fired '%s' event to %d subscribers", eventType, subscriberCount))
end

-- Get all subscribers for an event type
function EventSystem.GetSubscribers(eventType: TagEventType): { EventCallback }
  if not eventSubscribers[eventType] then
    return {}
  end

  local callbacks = {}
  for _, callback in pairs(eventSubscribers[eventType]) do
    table.insert(callbacks, callback)
  end

  return callbacks
end

-- Get event history
function EventSystem.GetEventHistory(): { TagEventData }
  return eventHistory
end

-- Get event metrics
function EventSystem.GetMetrics()
  return {
    eventsProcessed = eventMetrics.eventsProcessed,
    subscribersNotified = eventMetrics.subscribersNotified,
    errorCount = eventMetrics.errorCount,
    uptime = os.time() - eventMetrics.lastReset,
    activeSubscriptions = (function()
      local count = 0
      for _, subscribers in pairs(eventSubscribers) do
        for _ in pairs(subscribers) do
          count = count + 1
        end
      end
      return count
    end)(),
  }
end

-- Reset metrics
function EventSystem.ResetMetrics(): ()
  eventMetrics.eventsProcessed = 0
  eventMetrics.subscribersNotified = 0
  eventMetrics.errorCount = 0
  eventMetrics.lastReset = os.time()
  debugLog("Event metrics reset")
end

-- Clear all subscriptions (for cleanup)
function EventSystem.ClearAllSubscriptions(): ()
  for eventType in pairs(eventSubscribers) do
    eventSubscribers[eventType] = {}
  end
  debugLog("All event subscriptions cleared")
end

-- Helper function to create standard event data
function EventSystem.CreateEventData(
  player: Player,
  newRole: string,
  oldRole: string?,
  source: string,
  metadata: { [string]: any }?
): TagEventData
  return {
    Player = player,
    NewRole = newRole,
    OldRole = oldRole,
    Timestamp = os.time(),
    Source = source,
    Metadata = metadata,
  }
end

-- Initialize event system
if TBRDSConfig.Settings.EnableEventSystem then
  debugLog("Event system initialized")

  -- Clean up old events periodically
  if RunService:IsServer() then
    task.spawn(function()
      while true do
        task.wait(300) -- 5 minutes

        -- Remove events older than 1 hour
        local cutoffTime = os.time() - 3600
        local newHistory = {}

        for _, eventData in ipairs(eventHistory) do
          if eventData.Timestamp > cutoffTime then
            table.insert(newHistory, eventData)
          end
        end

        eventHistory = newHistory
        debugLog(string.format("Cleaned event history, %d events remaining", #eventHistory))
      end
    end)
  end
end

return EventSystem
