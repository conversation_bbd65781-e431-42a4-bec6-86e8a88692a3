-- soon: !strict

--[[
    TBRDS Public API Module

    Public interface for external systems to interact with TBRDS

    ARCHITECTURE ROLE:
    - Provides clean API for other systems to use TBRDS
    - Abstracts internal implementation details
    - Enables integration with MAFS, MCS, CGS, and other systems
    - Provides event subscription for system reactions

    USAGE:
    - Import this module to interact with TBRDS
    - Subscribe to tag events for system integration
    - Query player roles and tag data

    *Dynamic Innovative Studio*
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Import modules
local EventSystem = require(script.Parent.EventSystem)
local PerformanceMonitor = require(script.Parent.PerformanceMonitor)
local TBRDSConfig = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
local Types = require(script.Parent.Types)
local Utils = require(script.Parent.Utils)

type TBRDSApi = Types.TBRDSApi
type ValidationResult = Types.ValidationResult
type PlayerTagData = Types.PlayerTagData
type PerformanceMetrics = Types.PerformanceMetrics
type RoleStyle = Types.RoleStyle
type EventCallback = Types.EventCallback

local TBRDSAPI = {}

-- Service access (will be set by the service manager)
local serviceManager = nil

-- Debug logging
local function debugLog(message: string)
  if TBRDSConfig.Settings.DebugMode then
    print(string.format("[TBRDS:API]: %s", message))
  end
end

-- Initialize the API with service manager
function TBRDSAPI._Initialize(serviceManagerInstance)
  serviceManager = serviceManagerInstance
  debugLog("API initialized with service manager")
end

-- Get a service safely
local function getService(serviceName: string)
  if not serviceManager then
    warn("TBRDS API: Service manager not initialized")
    return nil
  end
  return serviceManager.GetService(serviceName)
end

-- Get a player's current role
function TBRDSAPI.GetPlayerRole(player: Player): string
  local validation = Utils.validatePlayer(player)
  if not validation.Success then
    return "User" -- Default role
  end

  -- Use TagService to get player role
  local tagService = getService("Tag")
  if tagService then
    local tagData = tagService.GetPlayerTagData(player)
    if tagData then
      return tagData.Role
    end
  end

  -- Fallback to RoleService
  local roleService = getService("Role")
  if roleService then
    return roleService.GetPlayerRole(player)
  end

  return "User"
end

-- Set a player's role (server-side only)
function TBRDSAPI.SetPlayerRole(player: Player, roleName: string): ValidationResult
  local result: ValidationResult = {
    Success = false,
    Role = nil,
    ErrorCode = nil,
    ErrorMessage = nil,
    SecurityFlags = nil,
  }

  -- Server-side only
  if not RunService:IsServer() then
    result.ErrorCode = TBRDSConfig.ErrorCodes.SECURITY_VIOLATION
    result.ErrorMessage = "SetPlayerRole can only be called on the server"
    return result
  end

  -- Validate player
  local playerValidation = Utils.validatePlayer(player)
  if not playerValidation.Success then
    return playerValidation
  end

  -- Validate role using RoleService
  local roleService = getService("Role")
  if not roleService then
    result.ErrorCode = TBRDSConfig.ErrorCodes.VALIDATION_FAILED
    result.ErrorMessage = "Role service not available"
    return result
  end

  local roleValidation = roleService.ValidatePlayerRole(player, roleName)
  if not roleValidation.Success then
    return roleValidation
  end

  -- Use TagService to assign the role
  local tagService = getService("Tag")
  if not tagService then
    result.ErrorCode = TBRDSConfig.ErrorCodes.VALIDATION_FAILED
    result.ErrorMessage = "Tag service not available"
    return result
  end

  return tagService.AssignTag(player)
end

-- Refresh a player's tag (re-validate and update)
function TBRDSAPI.RefreshPlayerTag(player: Player): ValidationResult
  local result: ValidationResult = {
    Success = false,
    Role = nil,
    ErrorCode = nil,
    ErrorMessage = nil,
    SecurityFlags = nil,
  }

  -- Validate player
  local playerValidation = Utils.validatePlayer(player)
  if not playerValidation.Success then
    return playerValidation
  end

  -- Use TagService to refresh the player's tag
  local tagService = getService("Tag")
  if not tagService then
    result.ErrorCode = TBRDSConfig.ErrorCodes.VALIDATION_FAILED
    result.ErrorMessage = "Tag service not available"
    return result
  end

  return tagService.RefreshPlayerTag(player)
end

-- Get player tag data
function TBRDSAPI.GetPlayerTagData(player: Player): PlayerTagData?
  local validation = Utils.validatePlayer(player)
  if not validation.Success then
    return nil
  end

  local tagService = getService("Tag")
  if tagService then
    return tagService.GetPlayerTagData(player)
  end

  return nil
end

-- Check if a role is valid for a player
function TBRDSAPI.IsRoleValid(player: Player, roleName: string): boolean
  local validation = Utils.validatePlayer(player)
  if not validation.Success then
    return false
  end

  local roleService = getService("Role")
  if roleService then
    local roleValidation = roleService.ValidatePlayerRole(player, roleName)
    return roleValidation.Success
  end

  return false
end

-- Get role style information
function TBRDSAPI.GetRoleStyle(roleName: string): RoleStyle?
  local roleService = getService("Role")
  if roleService then
    return roleService.GetRoleStyle(roleName)
  end

  return nil
end

-- Subscribe to tag change events
function TBRDSAPI.SubscribeToTagChanges(callback: EventCallback): string
  return EventSystem.Subscribe("TagChanged", callback)
end

-- Unsubscribe from tag change events
function TBRDSAPI.UnsubscribeFromTagChanges(subscriptionId: string): boolean
  return EventSystem.Unsubscribe(subscriptionId)
end

-- Get performance metrics
function TBRDSAPI.GetPerformanceMetrics(): PerformanceMetrics
  return PerformanceMonitor.GetMetrics()
end

-- Set debug mode
function TBRDSAPI.SetDebugMode(enabled: boolean): ()
  -- This would update the configuration in a real implementation
  debugLog(string.format("Debug mode %s", enabled and "enabled" or "disabled"))
end

-- Get all players with a specific role
function TBRDSAPI.GetPlayersWithRole(roleName: string): { Player }
  local roleService = getService("Role")
  if roleService then
    return roleService.GetPlayersWithRole(roleName)
  end

  return {}
end

-- Get role statistics
function TBRDSAPI.GetRoleStatistics(): { [string]: number }
  local tagService = getService("Tag")
  if tagService then
    local stats = tagService.GetTagStatistics()
    return stats.roleDistribution or {}
  end

  return {}
end

-- Check system health
function TBRDSAPI.GetSystemHealth(): { [string]: any }
  local health = {
    playersTracked = 0,
    cacheSize = 0,
    performanceMetrics = PerformanceMonitor.GetMetrics(),
    eventSystemMetrics = EventSystem.GetMetrics(),
    configurationValid = true,
    servicesHealthy = false,
  }

  -- Get service health if service manager is available
  if serviceManager then
    health.servicesHealthy = serviceManager.AreAllServicesHealthy()
    local serviceStatus = serviceManager.GetServiceStatusSummary()
    health.playersTracked = serviceStatus.services.Tag
        and serviceStatus.services.Tag.details
        and serviceStatus.services.Tag.details.totalPlayers
      or 0
    health.cacheSize = health.playersTracked
  end

  return health
end

-- Internal functions for backward compatibility (deprecated)
function TBRDSAPI._UpdatePlayerCache(player: Player, tagData: PlayerTagData): ()
  warn("TBRDS API: _UpdatePlayerCache is deprecated - use TagService directly")
end

function TBRDSAPI._RemovePlayerFromCache(player: Player): ()
  warn("TBRDS API: _RemovePlayerFromCache is deprecated - use TagService directly")
end

function TBRDSAPI._GetPlayerCache(): { [Player]: PlayerTagData }
  warn("TBRDS API: _GetPlayerCache is deprecated - use TagService.GetAllPlayerTags() instead")
  local tagService = getService("Tag")
  if tagService then
    return tagService.GetAllPlayerTags()
  end
  return {}
end

debugLog("API module loaded")

return TBRDSAPI
