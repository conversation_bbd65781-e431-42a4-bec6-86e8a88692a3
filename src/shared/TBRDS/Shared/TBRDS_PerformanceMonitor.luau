-- soon: !strict

--[[
    TBRDS Performance Monitor Module

    Performance tracking and metrics collection for TBRDS

    ARCHITECTURE ROLE:
    - Tracks system performance metrics
    - Monitors tag assignment efficiency
    - Provides debugging and optimization data
    - Enables performance-based system tuning

    USAGE:
    - Record performance events throughout the system
    - Monitor system health and efficiency
    - Generate performance reports

    *Dynamic Innovative Studio*
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Import configuration
local TBRDSConfig = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
local Types = require(script.Parent.Parent.TBRDS_Types)

type PerformanceMetrics = Types.PerformanceMetrics

local PerformanceMonitor = {}

-- Metrics storage
local metrics: PerformanceMetrics = {
	TagAssignments = 0,
	ValidationTime = 0,
	SecurityEvents = 0,
	CacheHits = 0,
	CacheMisses = 0,
	ErrorCount = 0,
	LastReset = os.time(),
}

-- Detailed metrics for analysis
local detailedMetrics = {
	validationTimes = {}, -- Array of validation times
	tagAssignmentsByRole = {}, -- Count by role
	errorsByType = {}, -- Count by error type
	securityEventsByType = {}, -- Count by security event type
	performanceHistory = {}, -- Historical snapshots
}

-- Performance thresholds
local PERFORMANCE_THRESHOLDS = {
	MaxValidationTime = 0.1, -- 100ms
	MaxTagAssignmentTime = 0.05, -- 50ms
	MaxCacheSize = 1000,
	MaxErrorRate = 0.05, -- 5%
}

-- Debug logging
local function debugLog(message: string)
	if TBRDSConfig.Settings.DebugMode and TBRDSConfig.Debug.LogPerformanceMetrics then
		print(string.format("[TBRDS:PerformanceMonitor]: %s", message))
	end
end

-- Record a tag assignment
function PerformanceMonitor.RecordTagAssignment(roleName: string?): ()
	metrics.TagAssignments = metrics.TagAssignments + 1

	if roleName then
		detailedMetrics.tagAssignmentsByRole[roleName] = (
			detailedMetrics.tagAssignmentsByRole[roleName] or 0
		) + 1
	end

	debugLog(string.format("Tag assignment recorded (total: %d)", metrics.TagAssignments))
end

-- Record validation time
function PerformanceMonitor.RecordValidationTime(timeMs: number): ()
	metrics.ValidationTime = metrics.ValidationTime + timeMs
	table.insert(detailedMetrics.validationTimes, timeMs)

	-- Keep only last 100 validation times
	if #detailedMetrics.validationTimes > 100 then
		table.remove(detailedMetrics.validationTimes, 1)
	end

	-- Check for performance issues
	if timeMs > PERFORMANCE_THRESHOLDS.MaxValidationTime * 1000 then
		warn(string.format("TBRDS: Slow validation detected: %.2fms", timeMs))
	end

	debugLog(string.format("Validation time recorded: %.2fms", timeMs))
end

-- Record a security event
function PerformanceMonitor.RecordSecurityEvent(eventType: string?): ()
	metrics.SecurityEvents = metrics.SecurityEvents + 1

	if eventType then
		detailedMetrics.securityEventsByType[eventType] = (
			detailedMetrics.securityEventsByType[eventType] or 0
		) + 1
	end

	debugLog(string.format("Security event recorded: %s", eventType or "unknown"))
end

-- Record a cache hit
function PerformanceMonitor.RecordCacheHit(): ()
	metrics.CacheHits = metrics.CacheHits + 1
	debugLog("Cache hit recorded")
end

-- Record a cache miss
function PerformanceMonitor.RecordCacheMiss(): ()
	metrics.CacheMisses = metrics.CacheMisses + 1
	debugLog("Cache miss recorded")
end

-- Record an error
function PerformanceMonitor.RecordError(errorType: string?): ()
	metrics.ErrorCount = metrics.ErrorCount + 1

	if errorType then
		detailedMetrics.errorsByType[errorType] = (detailedMetrics.errorsByType[errorType] or 0) + 1
	end

	debugLog(string.format("Error recorded: %s", errorType or "unknown"))
end

-- Get current metrics
function PerformanceMonitor.GetMetrics(): PerformanceMetrics
	return {
		TagAssignments = metrics.TagAssignments,
		ValidationTime = metrics.ValidationTime,
		SecurityEvents = metrics.SecurityEvents,
		CacheHits = metrics.CacheHits,
		CacheMisses = metrics.CacheMisses,
		ErrorCount = metrics.ErrorCount,
		LastReset = metrics.LastReset,
	}
end

-- Get detailed metrics
function PerformanceMonitor.GetDetailedMetrics()
	local cacheHitRate = 0
	if metrics.CacheHits + metrics.CacheMisses > 0 then
		cacheHitRate = metrics.CacheHits / (metrics.CacheHits + metrics.CacheMisses)
	end

	local avgValidationTime = 0
	if #detailedMetrics.validationTimes > 0 then
		local total = 0
		for _, time in ipairs(detailedMetrics.validationTimes) do
			total = total + time
		end
		avgValidationTime = total / #detailedMetrics.validationTimes
	end

	local uptime = os.time() - metrics.LastReset
	local errorRate = uptime > 0 and metrics.ErrorCount / uptime or 0

	return {
		basic = PerformanceMonitor.GetMetrics(),
		cacheHitRate = cacheHitRate,
		averageValidationTime = avgValidationTime,
		errorRate = errorRate,
		uptime = uptime,
		tagAssignmentsByRole = detailedMetrics.tagAssignmentsByRole,
		errorsByType = detailedMetrics.errorsByType,
		securityEventsByType = detailedMetrics.securityEventsByType,
		recentValidationTimes = detailedMetrics.validationTimes,
	}
end

-- Reset all metrics
function PerformanceMonitor.Reset(): ()
	metrics = {
		TagAssignments = 0,
		ValidationTime = 0,
		SecurityEvents = 0,
		CacheHits = 0,
		CacheMisses = 0,
		ErrorCount = 0,
		LastReset = os.time(),
	}

	detailedMetrics = {
		validationTimes = {},
		tagAssignmentsByRole = {},
		errorsByType = {},
		securityEventsByType = {},
		performanceHistory = {},
	}

	debugLog("Performance metrics reset")
end

-- Create a performance snapshot
function PerformanceMonitor.CreateSnapshot(): ()
	local snapshot = {
		timestamp = os.time(),
		metrics = PerformanceMonitor.GetMetrics(),
		detailed = PerformanceMonitor.GetDetailedMetrics(),
	}

	table.insert(detailedMetrics.performanceHistory, snapshot)

	-- Keep only last 24 snapshots (if taken hourly)
	if #detailedMetrics.performanceHistory > 24 then
		table.remove(detailedMetrics.performanceHistory, 1)
	end

	debugLog("Performance snapshot created")
end

-- Get performance history
function PerformanceMonitor.GetHistory()
	return detailedMetrics.performanceHistory
end

-- Check for performance issues
function PerformanceMonitor.CheckPerformanceHealth()
	local issues = {}
	local detailed = PerformanceMonitor.GetDetailedMetrics()

	-- Check validation time
	if detailed.averageValidationTime > PERFORMANCE_THRESHOLDS.MaxValidationTime * 1000 then
		table.insert(
			issues,
			string.format("High validation time: %.2fms", detailed.averageValidationTime)
		)
	end

	-- Check error rate
	if detailed.errorRate > PERFORMANCE_THRESHOLDS.MaxErrorRate then
		table.insert(issues, string.format("High error rate: %.2f%%", detailed.errorRate * 100))
	end

	-- Check cache performance
	if detailed.cacheHitRate < 0.8 and metrics.CacheHits + metrics.CacheMisses > 100 then
		table.insert(issues, string.format("Low cache hit rate: %.2f%%", detailed.cacheHitRate * 100))
	end

	return issues
end

-- Generate performance report
function PerformanceMonitor.GenerateReport(): string
	local detailed = PerformanceMonitor.GetDetailedMetrics()
	local issues = PerformanceMonitor.CheckPerformanceHealth()

	local report = {
		"=== TBRDS Performance Report ===",
		string.format("Uptime: %d seconds", detailed.uptime),
		string.format("Tag Assignments: %d", metrics.TagAssignments),
		string.format("Average Validation Time: %.2fms", detailed.averageValidationTime),
		string.format("Cache Hit Rate: %.2f%%", detailed.cacheHitRate * 100),
		string.format("Error Rate: %.2f%%", detailed.errorRate * 100),
		string.format("Security Events: %d", metrics.SecurityEvents),
		"",
		"=== Performance Issues ===",
	}

	if #issues > 0 then
		for _, issue in ipairs(issues) do
			table.insert(report, "⚠️ " .. issue)
		end
	else
		table.insert(report, "✅ No performance issues detected")
	end

	return table.concat(report, "\n")
end

-- Initialize performance monitoring
if TBRDSConfig.Settings.EnablePerformanceMetrics then
	debugLog("Performance monitoring initialized")

	-- Create periodic snapshots on server
	if RunService:IsServer() then
		task.spawn(function()
			while true do
				task.wait(3600) -- 1 hour
				PerformanceMonitor.CreateSnapshot()

				-- Check for performance issues
				local issues = PerformanceMonitor.CheckPerformanceHealth()
				if #issues > 0 then
					warn("TBRDS Performance Issues Detected:")
					for _, issue in ipairs(issues) do
						warn("  " .. issue)
					end
				end
			end
		end)
	end
end

return PerformanceMonitor
