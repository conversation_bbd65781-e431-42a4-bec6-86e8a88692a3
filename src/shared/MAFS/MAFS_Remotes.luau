--!strict

--[[
 - file: MAFS_REMOTES.LUAU

 - version: 2.0.0
 - author: BleckWolf25
 - contributors:

 - copyright: Dynamic Innovative Studio

 - description:
   - Shared module for the Modular Audio FootStep System (MAFS) remote communication.
   - Manages RemoteEvent creation, caching, and safe access for client-server communication.
   - Implements server-side creation with client-side waiting patterns for network objects.
   - Provides centralized remote management with automatic cleanup and error handling.
   - Ensures consistent event naming and structure across the MAFS system.
   - Used by both client and server components for footstep audio synchronization.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- ============================================================================
-- CONSTANTS
-- ============================================================================

local REMOTES_FOLDER_NAME = "MAFS"
local FOOTSTEP_EVENT_NAME = "FootstepEvent"
local WAIT_TIMEOUT = 10 -- Seconds to wait for remote objects

-- ============================================================================
-- TYPE ANNOTATIONS & DEFINITIONS
-- ============================================================================

-- Module interface for external usage
export type MAFSRemotesModule = {
  GetRemotesFolder: () -> Folder?,
  GetFootstepEvent: () -> RemoteEvent?,
  Initialize: () -> boolean,
  Cleanup: () -> (),
}

-- ============================================================================
-- MODULE DEFINITION
-- ============================================================================
local MAFSRemotes = {}

-- ============================================================================
-- MODULE STATE
-- ============================================================================

-- Cache for remote objects to avoid repeated lookups
-- These are set to nil initially and populated on first access
local remotesFolder: Folder? = nil :: Folder?
local footstepEvent: RemoteEvent? = nil :: RemoteEvent?

-- ============================================================================
-- REMOTE MANAGEMENT FUNCTIONS
-- ============================================================================

--[[
    Gets or creates the MAFS remotes folder

    @return (Folder?) - The MAFS remotes folder, or nil if creation/access failed
]]
function MAFSRemotes.GetRemotesFolder(): Folder?
  -- Return cached folder if it's still valid
  if remotesFolder and remotesFolder.Parent then
    return remotesFolder
  end

  -- Look for existing folder in ReplicatedStorage
  local foundFolder = ReplicatedStorage:FindFirstChild(REMOTES_FOLDER_NAME) :: Folder?

  if foundFolder then
    remotesFolder = foundFolder
    return remotesFolder
  end

  -- Create folder if we're on the server
  if RunService:IsServer() then
    local newFolder = Instance.new("Folder")
    newFolder.Name = REMOTES_FOLDER_NAME
    newFolder.Parent = ReplicatedStorage
    remotesFolder = newFolder
    return remotesFolder
  else
    -- Client waits for server to create the folder
    local waitedFolder =
      ReplicatedStorage:WaitForChild(REMOTES_FOLDER_NAME, WAIT_TIMEOUT) :: Folder?
    if waitedFolder then
      remotesFolder = waitedFolder
      return remotesFolder
    else
      warn("MAFS: Failed to find remotes folder after timeout")
      return nil
    end
  end
end

--[[
    Gets or creates the footstep RemoteEvent

    @return (RemoteEvent?) - The footstep RemoteEvent, or nil if creation/access failed
]]
function MAFSRemotes.GetFootstepEvent(): RemoteEvent?
  -- Return cached event if it's still valid
  if footstepEvent and footstepEvent.Parent then
    return footstepEvent
  end

  -- Get the remotes folder first
  local folder = MAFSRemotes.GetRemotesFolder()
  if not folder then
    warn("MAFS: Cannot access remotes folder")
    return nil
  end

  -- Look for existing RemoteEvent
  local foundEvent = folder:FindFirstChild(FOOTSTEP_EVENT_NAME) :: RemoteEvent?

  if foundEvent then
    footstepEvent = foundEvent
    return footstepEvent
  end

  -- Create RemoteEvent if we're on the server
  if RunService:IsServer() then
    local newEvent = Instance.new("RemoteEvent")
    newEvent.Name = FOOTSTEP_EVENT_NAME
    newEvent.Parent = folder
    footstepEvent = newEvent
    return footstepEvent
  else
    -- Client waits for server to create the RemoteEvent
    local waitedEvent = folder:WaitForChild(FOOTSTEP_EVENT_NAME, WAIT_TIMEOUT) :: RemoteEvent?
    if waitedEvent then
      footstepEvent = waitedEvent
      return footstepEvent
    else
      warn("MAFS: Failed to find footstep event after timeout")
      return nil
    end
  end
end

-- ============================================================================
-- INITIALIZATION FUNCTIONS
-- ============================================================================

--[[
    Initializes the remotes system
    Should be called during system startup

    @return (boolean) - True if initialization succeeded, false otherwise
]]
function MAFSRemotes.Initialize(): boolean
  -- Attempt to get or create the remotes folder
  local folder = MAFSRemotes.GetRemotesFolder()
  if not folder then
    warn("MAFS: Failed to initialize remotes folder")
    return false
  end

  -- Attempt to get or create the footstep event
  local event = MAFSRemotes.GetFootstepEvent()
  if not event then
    warn("MAFS: Failed to initialize footstep event")
    return false
  end

  -- Log successful initialization
  if RunService:IsServer() then
    print("MAFS: Server remotes initialized successfully")
  else
    print("MAFS: Client remotes connected successfully")
  end

  return true
end

-- ============================================================================
-- CLEANUP FUNCTIONS
-- ============================================================================

--[[
    Cleans up remote references
    Used for testing or system shutdown
]]
function MAFSRemotes.Cleanup(): ()
  remotesFolder = nil :: Folder?
  footstepEvent = nil :: RemoteEvent?

  if RunService:IsServer() then
    print("MAFS: Server remotes cleaned up")
  else
    print("MAFS: Client remotes disconnected")
  end
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return MAFSRemotes :: MAFSRemotesModule
