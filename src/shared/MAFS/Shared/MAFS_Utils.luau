--!strict

--[[
 - file: MAFS_UTILS.LUAU

 - version: 2.1.0
 - author: BleckWolf25
 - contributors:

 - copyright: Dynamic Innovative Studio

 - description:
   - Shared utility module for the Modular Audio FootStep System (MAFS).
   - Provides material configuration utilities and sound pool management for performance optimization.
   - Enables custom material assignments for parts, models, and batch operations.
   - Implements sound object pooling with performance tracking and metrics collection.
   - Supports pattern-based material assignment and validation systems.
   - Used by both client and server components for material setup and sound management.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS_Configuration)
local PerformanceMonitor = require(
  ReplicatedStorage:WaitForChild("MAFS")
    :WaitForChild("Shared")
    :WaitForChild("MAFS_PerformanceMonitor")
)

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local MAX_CACHED_SOUNDS = 20 -- Maximum number of sounds to keep in the pool
local FOOTSTEP_MATERIAL_ATTRIBUTE = "FootstepMaterial" -- Attribute name for custom materials

-- ============================================================================
-- TYPES
-- ============================================================================

-- Performance statistics structure
type PerformanceStats = {
  activeSounds: number,
  totalSoundsCreated: number,
  poolHits: number,
}

-- Module interface for external usage
export type UtilsModule = {
  GetSound: (template: Sound) -> Sound?,
  ReturnSound: (sound: Sound) -> (),
  CleanupSoundPool: () -> (),
  GetPerformanceMetrics: () -> PerformanceStats,
  TrackSoundCreated: () -> (),
  TrackSoundDestroyed: () -> (),
  SetPartMaterial: (part: BasePart, materialName: string) -> (),
  SetModelMaterial: (model: Model, materialName: string) -> (),
  SetPartsByNamePattern: (parent: Instance, namePattern: string, materialName: string) -> (),
  SetPartsByMaterial: (parent: Instance, material: Enum.Material, materialName: string) -> (),
  ValidateMaterialName: (materialName: string) -> boolean,
  IsDebugMode: () -> boolean,
}

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Utils = {}

-- ============================================================================
-- MODULE STATE
-- ============================================================================

-- Sound pool for performance optimization
local cachedSounds: { Sound } = {}

-- Performance tracking statistics
local stats: PerformanceStats = {
  activeSounds = 0,
  totalSoundsCreated = 0,
  poolHits = 0,
}

-- ============================================================================
-- SOUND POOL MANAGEMENT FUNCTIONS
-- ============================================================================

--[[
    Gets a sound object from the pool or creates a new one

    @param template (Sound) - The sound template to clone
    @return (Sound?) - A sound object ready for use, or nil if failed
]]
function Utils.GetSound(template: Sound): Sound?
  if not template or not template:IsA("Sound") then
    warn("MAFS: Invalid sound template provided")
    return nil
  end

  -- Try to get a sound from the pool first
  if #cachedSounds > 0 then
    local sound = table.remove(cachedSounds) :: Sound?
    if sound and sound.Parent then
      PerformanceMonitor.TrackPoolHit()
      return sound
    end
  end

  -- Create a new sound if pool is empty
  local newSound = template:Clone()
  if newSound then
    stats.totalSoundsCreated = stats.totalSoundsCreated + 1
    PerformanceMonitor.TrackPoolMiss()
    return newSound
  end

  return nil
end

--[[
    Returns a sound object to the pool for reuse

    @param sound (Sound) - The sound object to return to the pool
]]
function Utils.ReturnSound(sound: Sound): ()
  if not sound or not sound:IsA("Sound") then
    return
  end

  -- Add to pool if there's space, otherwise destroy
  if #cachedSounds < MAX_CACHED_SOUNDS then
    sound.Playing = false
    sound.TimePosition = 0
    table.insert(cachedSounds, sound)
    stats.poolHits = stats.poolHits + 1
  else
    sound:Destroy()
  end
end

--[[
    Cleans up the entire sound pool

    Destroys all cached sounds and clears the pool.
]]
function Utils.CleanupSoundPool(): ()
  for _, sound in cachedSounds do
    sound:Destroy()
  end
  table.clear(cachedSounds)
end

-- ============================================================================
-- PERFORMANCE TRACKING FUNCTIONS
-- ============================================================================

--[[
    Gets current performance metrics

    @return (PerformanceStats) - Current performance statistics
]]
function Utils.GetPerformanceMetrics(): PerformanceStats
  return stats
end

--[[
    Tracks the creation of a new sound object

    Updates internal statistics and notifies the performance monitor.
]]
function Utils.TrackSoundCreated(): ()
  stats.totalSoundsCreated = stats.totalSoundsCreated + 1
  stats.activeSounds = stats.activeSounds + 1
  PerformanceMonitor.TrackSoundCreated()
end

--[[
    Tracks the destruction of a sound object

    Updates internal statistics and notifies the performance monitor.
]]
function Utils.TrackSoundDestroyed(): ()
  stats.activeSounds = math.max(0, stats.activeSounds - 1)
  PerformanceMonitor.TrackSoundDestroyed()
end

-- ============================================================================
-- MATERIAL CONFIGURATION FUNCTIONS
-- ============================================================================

--[[
    Sets a custom footstep material for a single part

    @param part (BasePart) - The part to configure
    @param materialName (string) - The custom material name to assign

    Example:
        local part = workspace.StonePath
        Utils.SetPartMaterial(part, "Stone")
]]
function Utils.SetPartMaterial(part: BasePart, materialName: string): ()
  if not part then
    warn("MAFS: Invalid part provided")
    return
  end
  if not materialName or not Utils.ValidateMaterialName(materialName) then
    warn("MAFS: Invalid material name:", materialName)
    return
  end

  part:SetAttribute(FOOTSTEP_MATERIAL_ATTRIBUTE, materialName)
end

--[[
    Sets a custom footstep material for all parts in a model

    @param model (Model) - The model containing parts to configure
    @param materialName (string) - The custom material name to assign

    Example:
        local bridge = workspace.WoodenBridge
        Utils.SetModelMaterial(bridge, "WoodPlanks")
]]
function Utils.SetModelMaterial(model: Model, materialName: string): ()
  if not model or not materialName then
    return
  end

  for _, part in model:GetDescendants() do
    if part:IsA("BasePart") then
      part:SetAttribute(FOOTSTEP_MATERIAL_ATTRIBUTE, materialName)
    end
  end
end

--[[
    Checks if debug mode is enabled

    @return (boolean) - True if debug mode is enabled
]]
function Utils.IsDebugMode(): boolean
  return MAFSConfig.IsDebugMode()
end

--[[
    Sets a custom footstep material for parts matching a name pattern

    @param parent (Instance) - The parent to search for matching parts
    @param namePattern (string) - Lua pattern to match part names against
    @param materialName (string) - The custom material name to assign

    Example:
        Utils.SetPartsByNamePattern(workspace, "^Snow_", "Snow")
        -- This would match parts named "Snow_Pile", "Snow_Ground", etc.
]]
function Utils.SetPartsByNamePattern(
  parent: Instance,
  namePattern: string,
  materialName: string
): ()
  if not parent or not namePattern or not materialName then
    return
  end

  for _, part in parent:GetDescendants() do
    if part:IsA("BasePart") and string.match(part.Name, namePattern) then
      part:SetAttribute(FOOTSTEP_MATERIAL_ATTRIBUTE, materialName)
    end
  end
end

--[[
    Sets a custom footstep material for all parts with a specific material type

    @param parent (Instance) - The parent to search for matching parts
    @param material (Enum.Material) - The Roblox material type to match
    @param materialName (string) - The custom material name to assign

    Example:
        Utils.SetPartsByMaterial(workspace, Enum.Material.Sand, "DeepSand")
        -- This would make all sand parts use the "DeepSand" footstep sound
]]
function Utils.SetPartsByMaterial(
  parent: Instance,
  material: Enum.Material,
  materialName: string
): ()
  if not parent or not material or not materialName then
    return
  end

  for _, part in parent:GetDescendants() do
    if part:IsA("BasePart") and part.Material == material then
      part:SetAttribute(FOOTSTEP_MATERIAL_ATTRIBUTE, materialName)
    end
  end
end

-- ============================================================================
-- VALIDATION FUNCTIONS
-- ============================================================================

--[[
    Validates if a material name exists in the configuration

    @param materialName (string) - The material name to validate
    @return (boolean) - True if the material name is valid
]]
function Utils.ValidateMaterialName(materialName: string): boolean
  -- Check if it's a valid custom material
  return MAFSConfig.CustomMaterials[materialName] ~= nil
end

-- ============================================================================
-- USAGE EXAMPLES
-- ============================================================================

--[[
    USAGE EXAMPLES:

    -- Configure a snow environment
    local snowEnvironment = workspace.SnowEnvironment
    Utils.SetModelMaterial(snowEnvironment, "Snow")

    -- Set all marble materials to use the Marble footstep sound
    Utils.SetPartsByMaterial(workspace, Enum.Material.Marble, "Marble")

    -- Configure a metal grate bridge
    local grate = workspace.MetalGrateBridge
    Utils.SetModelMaterial(grate, "MetalGrate")

    -- Set parts matching a pattern
    Utils.SetPartsByNamePattern(workspace, "Wood", "WoodPlanks")

    -- Configure individual parts
    Utils.SetPartMaterial(workspace.CustomFloor, "Stone")
]]

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Utils :: UtilsModule
