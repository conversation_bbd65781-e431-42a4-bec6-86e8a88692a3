--!strict

--[[
 - file: MAFS_MATERIAL_CONFIGURATION.LUAU

 - version: 2.1.0
 - author: BleckWolf25
 - contributors:

 - copyright: Dynamic Innovative Studio

 - description:
   - Shared material configuration module for the Modular Audio FootStep System (MAFS).
   - Provides advanced material detection and hierarchical resolution with performance optimization.
   - Supports custom material attributes, batch configuration operations, and validation systems.
   - Implements raycast-based material detection with fallback mechanisms for robust material resolution.
   - Enables bulk material assignment operations with pattern matching and material type filtering.
   - Used by both client and server components for material setup and runtime material detection.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS_Configuration)
local MAFSUtils =
  require(ReplicatedStorage:WaitForChild("MAFS"):WaitForChild("Shared"):WaitForChild("MAFS_Utils"))
local PerformanceMonitor = require(
  ReplicatedStorage:WaitForChild("MAFS")
    :WaitForChild("Shared")
    :WaitForChild("MAFS_PerformanceMonitor")
)

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local FOOTSTEP_MATERIAL_ATTRIBUTE = "FootstepMaterial" -- Attribute name for custom materials
local RAYCAST_DISTANCE = 5 -- Maximum distance for material detection raycast
local RAYCAST_OFFSET = Vector3.new(0, 1, 0) -- Offset for raycast origin
local RAYCAST_DIRECTION = Vector3.new(0, -RAYCAST_DISTANCE, 0) -- Raycast direction vector

-- ============================================================================
-- TYPES
-- ============================================================================

-- Material data structure from configuration
type MaterialData = {
  SoundId: string,
  Volume: number?,
  Pitch: number?,
  [string]: any,
}

-- Material resolution result
type MaterialResolutionResult = {
  materialData: MaterialData,
  resolutionType: string,
  sourcePart: BasePart?,
  customMaterialName: string?,
}

-- Module interface for external usage
export type MaterialConfigModule = {
  ResolveMaterial: (position: Vector3, humanoid: Humanoid?) -> MaterialData,
  ResolveMaterialDetailed: (position: Vector3, humanoid: Humanoid?) -> MaterialResolutionResult,
  ConfigurePart: (part: BasePart, materialName: string) -> boolean,
  ConfigureModel: (model: Instance, materialName: string) -> number,
  ConfigureByNamePattern: (parent: Instance, namePattern: string, materialName: string) -> number,
  ConfigureByRobloxMaterial: (
    parent: Instance,
    robloxMaterial: Enum.Material,
    materialName: string
  ) -> number,
  ClearPartMaterial: (part: BasePart) -> boolean,
  GetPartMaterial: (part: BasePart) -> string?,
  ValidateConfiguration: (parent: Instance) -> { valid: number, invalid: number, missing: number },
  GetMaterialStatistics: (parent: Instance) -> { [string]: number },
}

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local MaterialConfig = {}

-- ============================================================================
-- MATERIAL RESOLUTION FUNCTIONS
-- ============================================================================

--[[
    Resolves the material for a given position with hierarchical fallback

    @param position (Vector3) - The world position to check
    @param humanoid (Humanoid?) - Optional humanoid for floor material fallback
    @return (MaterialData) - Material data containing sound configuration
]]
function MaterialConfig.ResolveMaterial(position: Vector3, humanoid: Humanoid?): MaterialData
  PerformanceMonitor.TrackMaterialResolution("attempt")

  -- Step 1: Check for custom material attributes via raycasting
  PerformanceMonitor.TrackRaycastOperation()
  local raycast = workspace:Raycast(position + RAYCAST_OFFSET, RAYCAST_DIRECTION)
  if raycast and raycast.Instance then
    local part = raycast.Instance

    -- Check for custom material attribute
    PerformanceMonitor.TrackAttributeLookup()
    local customMaterial = part:GetAttribute(FOOTSTEP_MATERIAL_ATTRIBUTE)
    if customMaterial then
      local materialData = MAFSConfig.GetMaterialData(customMaterial)
      if materialData then
        PerformanceMonitor.TrackMaterialResolution("custom")
        if MAFSConfig.IsDebugMode() then
          print(
            string.format(
              "MAFS: Using custom material '%s' for part '%s'",
              customMaterial,
              part.Name
            )
          )
        end
        return materialData
      end
    end

    -- Step 2: Check part's Roblox material
    if part.Material and part.Material ~= Enum.Material.Air then
      local materialData = MAFSConfig.GetMaterialData(part.Material)
      if materialData then
        PerformanceMonitor.TrackMaterialResolution("roblox")
        if MAFSConfig.IsDebugMode() then
          print(
            string.format(
              "MAFS: Using Roblox material '%s' for part '%s'",
              tostring(part.Material),
              part.Name
            )
          )
        end
        return materialData
      end
    end
  end

  -- Step 3: Fall back to humanoid floor material if available
  if humanoid and humanoid.FloorMaterial and humanoid.FloorMaterial ~= Enum.Material.Air then
    local materialData = MAFSConfig.GetMaterialData(humanoid.FloorMaterial)
    if materialData then
      PerformanceMonitor.TrackMaterialResolution("roblox")
      if MAFSConfig.IsDebugMode() then
        print(
          string.format(
            "MAFS: Using humanoid floor material '%s'",
            tostring(humanoid.FloorMaterial)
          )
        )
      end
      return materialData
    end
  end

  -- Step 4: Return default material
  PerformanceMonitor.TrackMaterialResolution("default")
  if MAFSConfig.IsDebugMode() then
    print("MAFS: Using default material")
  end
  return MAFSConfig.DefaultMaterial
end

--[[
    Resolves material with detailed information about the resolution process

    @param position (Vector3) - The world position to check
    @param humanoid (Humanoid?) - Optional humanoid for floor material fallback
    @return (MaterialResolutionResult) - Detailed resolution result with metadata
]]
function MaterialConfig.ResolveMaterialDetailed(
  position: Vector3,
  humanoid: Humanoid?
): MaterialResolutionResult
  PerformanceMonitor.TrackMaterialResolution("attempt")

  -- Step 1: Check for custom material attributes via raycasting
  PerformanceMonitor.TrackRaycastOperation()
  local raycast = workspace:Raycast(position + RAYCAST_OFFSET, RAYCAST_DIRECTION)
  if raycast and raycast.Instance then
    local part = raycast.Instance

    -- Check for custom material attribute
    PerformanceMonitor.TrackAttributeLookup()
    local customMaterial = part:GetAttribute(FOOTSTEP_MATERIAL_ATTRIBUTE)
    if customMaterial then
      local materialData = MAFSConfig.GetMaterialData(customMaterial)
      if materialData then
        PerformanceMonitor.TrackMaterialResolution("custom")
        return {
          materialData = materialData,
          resolutionType = "custom",
          sourcePart = part,
          customMaterialName = customMaterial,
        }
      end
    end

    -- Step 2: Check part's Roblox material
    if part.Material and part.Material ~= Enum.Material.Air then
      local materialData = MAFSConfig.GetMaterialData(part.Material)
      if materialData then
        PerformanceMonitor.TrackMaterialResolution("roblox")
        return {
          materialData = materialData,
          resolutionType = "roblox",
          sourcePart = part,
          customMaterialName = nil,
        }
      end
    end
  end

  -- Step 3: Fall back to humanoid floor material if available
  if humanoid and humanoid.FloorMaterial and humanoid.FloorMaterial ~= Enum.Material.Air then
    local materialData = MAFSConfig.GetMaterialData(humanoid.FloorMaterial)
    if materialData then
      PerformanceMonitor.TrackMaterialResolution("roblox")
      return {
        materialData = materialData,
        resolutionType = "humanoid",
        sourcePart = nil,
        customMaterialName = nil,
      }
    end
  end

  -- Step 4: Return default material
  PerformanceMonitor.TrackMaterialResolution("default")
  return {
    materialData = MAFSConfig.DefaultMaterial,
    resolutionType = "default",
    sourcePart = nil,
    customMaterialName = nil,
  }
end

-- ============================================================================
-- MATERIAL CONFIGURATION FUNCTIONS
-- ============================================================================

--[[
    Configures a single part with a custom footstep material

    @param part (BasePart) - The part to configure
    @param materialName (string) - The custom material name
    @return (boolean) - Success status
]]
function MaterialConfig.ConfigurePart(part: BasePart, materialName: string): boolean
  if not part or not part:IsA("BasePart") then
    warn("MAFS: Invalid part provided to ConfigurePart")
    return false
  end

  if not MAFSUtils.ValidateMaterialName(materialName) then
    warn(string.format("MAFS: Invalid material name '%s'", tostring(materialName)))
    return false
  end

  part:SetAttribute(FOOTSTEP_MATERIAL_ATTRIBUTE, materialName)

  if MAFSConfig.IsDebugMode() then
    print(string.format("MAFS: Configured part '%s' with material '%s'", part.Name, materialName))
  end

  return true
end

--[[
    Configures all parts in a model with a custom footstep material

    @param model (Instance) - The model containing parts to configure
    @param materialName (string) - The custom material name
    @return (number) - Number of parts configured
]]
function MaterialConfig.ConfigureModel(model: Instance, materialName: string): number
  if not model then
    warn("MAFS: Invalid model provided to ConfigureModel")
    return 0
  end

  if not MAFSUtils.ValidateMaterialName(materialName) then
    warn(string.format("MAFS: Invalid material name '%s'", tostring(materialName)))
    return 0
  end

  local configuredCount = 0

  for _, descendant in model:GetDescendants() do
    if descendant:IsA("BasePart") then
      descendant:SetAttribute(FOOTSTEP_MATERIAL_ATTRIBUTE, materialName)
      configuredCount = configuredCount + 1
    end
  end

  if MAFSConfig.IsDebugMode() then
    print(
      string.format(
        "MAFS: Configured %d parts in model '%s' with material '%s'",
        configuredCount,
        model.Name,
        materialName
      )
    )
  end

  return configuredCount
end

--[[
    Configures parts matching a name pattern with a custom footstep material

    @param parent (Instance) - The parent to search for matching parts
    @param namePattern (string) - Lua pattern to match part names
    @param materialName (string) - The custom material name
    @return (number) - Number of parts configured
]]
function MaterialConfig.ConfigureByNamePattern(
  parent: Instance,
  namePattern: string,
  materialName: string
): number
  if not parent or not namePattern or not MAFSUtils.ValidateMaterialName(materialName) then
    warn("MAFS: Invalid parameters provided to ConfigureByNamePattern")
    return 0
  end

  local configuredCount = 0

  for _, descendant in parent:GetDescendants() do
    if descendant:IsA("BasePart") and string.match(descendant.Name, namePattern) then
      descendant:SetAttribute(FOOTSTEP_MATERIAL_ATTRIBUTE, materialName)
      configuredCount = configuredCount + 1
    end
  end

  if MAFSConfig.IsDebugMode() then
    print(
      string.format(
        "MAFS: Configured %d parts matching pattern '%s' with material '%s'",
        configuredCount,
        namePattern,
        materialName
      )
    )
  end

  return configuredCount
end

--[[
    Configures parts with a specific Roblox material to use a custom footstep material

    @param parent (Instance) - The parent to search for matching parts
    @param robloxMaterial (Enum.Material) - The Roblox material to match
    @param materialName (string) - The custom material name
    @return (number) - Number of parts configured
]]
function MaterialConfig.ConfigureByRobloxMaterial(
  parent: Instance,
  robloxMaterial: Enum.Material,
  materialName: string
): number
  if not parent or not robloxMaterial or not MAFSUtils.ValidateMaterialName(materialName) then
    warn("MAFS: Invalid parameters provided to ConfigureByRobloxMaterial")
    return 0
  end

  local configuredCount = 0

  for _, descendant in parent:GetDescendants() do
    if descendant:IsA("BasePart") and descendant.Material == robloxMaterial then
      descendant:SetAttribute(FOOTSTEP_MATERIAL_ATTRIBUTE, materialName)
      configuredCount = configuredCount + 1
    end
  end

  if MAFSConfig.IsDebugMode() then
    print(
      string.format(
        "MAFS: Configured %d parts with Roblox material '%s' to use custom material '%s'",
        configuredCount,
        tostring(robloxMaterial),
        materialName
      )
    )
  end

  return configuredCount
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

--[[
    Removes custom material configuration from a part

    @param part (BasePart) - The part to clear
    @return (boolean) - Success status
]]
function MaterialConfig.ClearPartMaterial(part: BasePart): boolean
  if not part or not part:IsA("BasePart") then
    warn("MAFS: Invalid part provided to ClearPartMaterial")
    return false
  end

  part:SetAttribute(FOOTSTEP_MATERIAL_ATTRIBUTE, nil)

  if MAFSConfig.IsDebugMode() then
    print(string.format("MAFS: Cleared custom material from part '%s'", part.Name))
  end

  return true
end

--[[
    Gets the current material configuration for a part

    @param part (BasePart) - The part to check
    @return (string?) - The custom material name, or nil if not configured
]]
function MaterialConfig.GetPartMaterial(part: BasePart): string?
  if not part or not part:IsA("BasePart") then
    return nil
  end

  return part:GetAttribute(FOOTSTEP_MATERIAL_ATTRIBUTE)
end

-- ============================================================================
-- VALIDATION AND ANALYSIS FUNCTIONS
-- ============================================================================

--[[
    Validates material configurations in a hierarchy

    @param parent (Instance) - The parent instance to validate
    @return (table) - Validation results with counts of valid, invalid, and missing configurations
]]
function MaterialConfig.ValidateConfiguration(
  parent: Instance
): { valid: number, invalid: number, missing: number }
  local results = {
    valid = 0,
    invalid = 0,
    missing = 0,
  }

  for _, descendant in parent:GetDescendants() do
    if descendant:IsA("BasePart") then
      local customMaterial = descendant:GetAttribute(FOOTSTEP_MATERIAL_ATTRIBUTE)
      if customMaterial then
        if MAFSUtils.ValidateMaterialName(customMaterial) then
          results.valid = results.valid + 1
        else
          results.invalid = results.invalid + 1
          if MAFSConfig.IsDebugMode() then
            warn(
              string.format(
                "MAFS: Invalid material '%s' on part '%s'",
                customMaterial,
                descendant.Name
              )
            )
          end
        end
      else
        results.missing = results.missing + 1
      end
    end
  end

  return results
end

--[[
    Gets statistics about material usage in a hierarchy

    @param parent (Instance) - The parent instance to analyze
    @return (table) - Dictionary of material names and their usage counts
]]
function MaterialConfig.GetMaterialStatistics(parent: Instance): { [string]: number }
  local statistics: { [string]: number } = {}

  for _, descendant in parent:GetDescendants() do
    if descendant:IsA("BasePart") then
      local customMaterial = descendant:GetAttribute(FOOTSTEP_MATERIAL_ATTRIBUTE)
      if customMaterial then
        statistics[customMaterial] = (statistics[customMaterial] or 0) + 1
      else
        -- Count Roblox materials for parts without custom materials
        local robloxMaterial = tostring(descendant.Material)
        local key = "Roblox:" .. robloxMaterial
        statistics[key] = (statistics[key] or 0) + 1
      end
    end
  end

  return statistics
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return MaterialConfig :: MaterialConfigModule
