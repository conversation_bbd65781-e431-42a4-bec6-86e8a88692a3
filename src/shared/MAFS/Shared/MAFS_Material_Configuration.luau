--!strict

--[[
    MAFS_Material_Configuration.luau

    Material configuration system for MAFS

    ARCHITECTURE ROLE:
    - Provides advanced material detection and configuration
    - Supports hierarchical material resolution
    - Enables bulk material configuration operations

    FEATURES:
    - Custom material attributes support
    - Batch configuration operations
    - Material validation and fallback systems
    - Performance-optimized material detection

    *Dynamic Innovative Studio*
]]

local MaterialConfig = {}

-- Services
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Import MAFS configuration
local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS_Configuration)
local MAFSUtils = require(ReplicatedStorage.MAFS.Shared.MAFS_Utils)
local PerformanceMonitor = require(ReplicatedStorage.MAFS.Shared.MAFS_PerformanceMonitor)

--[[
    Resolves the material for a given position with hierarchical fallback

    @param position (Vector3) - The world position to check
    @param humanoid (Humanoid) - Optional humanoid for floor material fallback
    @return (table) - Material data containing sound configuration
]]
function MaterialConfig.ResolveMaterial(position, humanoid)
  PerformanceMonitor.TrackMaterialResolution("attempt")

  -- Step 1: Check for custom material attributes via raycasting
  PerformanceMonitor.TrackRaycastOperation()
  local raycast = workspace:Raycast(position + Vector3.new(0, 1, 0), Vector3.new(0, -5, 0))
  if raycast and raycast.Instance then
    local part = raycast.Instance

    -- Check for custom material attribute
    local customMaterial = part:GetAttribute("FootstepMaterial")
    if customMaterial then
      local materialData = MAFSConfig.GetMaterialData(customMaterial)
      if materialData then
        if MAFSConfig.IsDebugMode() then
          print(
            string.format(
              "MAFS: Using custom material '%s' for part '%s'",
              customMaterial,
              part.Name
            )
          )
        end
        return materialData
      end
    end

    -- Step 2: Check part's Roblox material
    if part.Material and part.Material ~= Enum.Material.Air then
      local materialData = MAFSConfig.GetMaterialData(part.Material)
      if materialData then
        if MAFSConfig.IsDebugMode() then
          print(
            string.format(
              "MAFS: Using Roblox material '%s' for part '%s'",
              tostring(part.Material),
              part.Name
            )
          )
        end
        return materialData
      end
    end
  end

  -- Step 3: Fall back to humanoid floor material if available
  if humanoid and humanoid.FloorMaterial and humanoid.FloorMaterial ~= Enum.Material.Air then
    local materialData = MAFSConfig.GetMaterialData(humanoid.FloorMaterial)
    if materialData then
      if MAFSConfig.IsDebugMode() then
        print(
          string.format(
            "MAFS: Using humanoid floor material '%s'",
            tostring(humanoid.FloorMaterial)
          )
        )
      end
      return materialData
    end
  end

  -- Step 4: Return default material
  if MAFSConfig.IsDebugMode() then
    print("MAFS: Using default material")
  end
  return MAFSConfig.DefaultMaterial
end

--[[
    Configures a single part with a custom footstep material

    @param part (BasePart) - The part to configure
    @param materialName (string) - The custom material name
    @return (boolean) - Success status
]]
function MaterialConfig.ConfigurePart(part, materialName)
  if not part or not part:IsA("BasePart") then
    warn("MAFS: Invalid part provided to ConfigurePart")
    return false
  end

  if not MAFSUtils.ValidateMaterialName(materialName) then
    warn(string.format("MAFS: Invalid material name '%s'", tostring(materialName)))
    return false
  end

  part:SetAttribute("FootstepMaterial", materialName)

  if MAFSConfig.IsDebugMode() then
    print(string.format("MAFS: Configured part '%s' with material '%s'", part.Name, materialName))
  end

  return true
end

--[[
    Configures all parts in a model with a custom footstep material

    @param model (Instance) - The model containing parts to configure
    @param materialName (string) - The custom material name
    @return (number) - Number of parts configured
]]
function MaterialConfig.ConfigureModel(model, materialName)
  if not model then
    warn("MAFS: Invalid model provided to ConfigureModel")
    return 0
  end

  if not MAFSUtils.ValidateMaterialName(materialName) then
    warn(string.format("MAFS: Invalid material name '%s'", tostring(materialName)))
    return 0
  end

  local configuredCount = 0

  for _, descendant in pairs(model:GetDescendants()) do
    if descendant:IsA("BasePart") then
      descendant:SetAttribute("FootstepMaterial", materialName)
      configuredCount = configuredCount + 1
    end
  end

  if MAFSConfig.IsDebugMode() then
    print(
      string.format(
        "MAFS: Configured %d parts in model '%s' with material '%s'",
        configuredCount,
        model.Name,
        materialName
      )
    )
  end

  return configuredCount
end

--[[
    Configures parts matching a name pattern with a custom footstep material

    @param parent (Instance) - The parent to search for matching parts
    @param namePattern (string) - Lua pattern to match part names
    @param materialName (string) - The custom material name
    @return (number) - Number of parts configured
]]
function MaterialConfig.ConfigureByNamePattern(parent, namePattern, materialName)
  if not parent or not namePattern or not MAFSUtils.ValidateMaterialName(materialName) then
    warn("MAFS: Invalid parameters provided to ConfigureByNamePattern")
    return 0
  end

  local configuredCount = 0

  for _, descendant in pairs(parent:GetDescendants()) do
    if descendant:IsA("BasePart") and string.match(descendant.Name, namePattern) then
      descendant:SetAttribute("FootstepMaterial", materialName)
      configuredCount = configuredCount + 1
    end
  end

  if MAFSConfig.IsDebugMode() then
    print(
      string.format(
        "MAFS: Configured %d parts matching pattern '%s' with material '%s'",
        configuredCount,
        namePattern,
        materialName
      )
    )
  end

  return configuredCount
end

--[[
    Configures parts with a specific Roblox material to use a custom footstep material

    @param parent (Instance) - The parent to search for matching parts
    @param robloxMaterial (Enum.Material) - The Roblox material to match
    @param materialName (string) - The custom material name
    @return (number) - Number of parts configured
]]
function MaterialConfig.ConfigureByRobloxMaterial(parent, robloxMaterial, materialName)
  if not parent or not robloxMaterial or not MAFSUtils.ValidateMaterialName(materialName) then
    warn("MAFS: Invalid parameters provided to ConfigureByRobloxMaterial")
    return 0
  end

  local configuredCount = 0

  for _, descendant in pairs(parent:GetDescendants()) do
    if descendant:IsA("BasePart") and descendant.Material == robloxMaterial then
      descendant:SetAttribute("FootstepMaterial", materialName)
      configuredCount = configuredCount + 1
    end
  end

  if MAFSConfig.IsDebugMode() then
    print(
      string.format(
        "MAFS: Configured %d parts with Roblox material '%s' to use custom material '%s'",
        configuredCount,
        tostring(robloxMaterial),
        materialName
      )
    )
  end

  return configuredCount
end

--[[
    Removes custom material configuration from a part

    @param part (BasePart) - The part to clear
    @return (boolean) - Success status
]]
function MaterialConfig.ClearPartMaterial(part)
  if not part or not part:IsA("BasePart") then
    warn("MAFS: Invalid part provided to ClearPartMaterial")
    return false
  end

  part:SetAttribute("FootstepMaterial", nil)

  if MAFSConfig.IsDebugMode() then
    print(string.format("MAFS: Cleared custom material from part '%s'", part.Name))
  end

  return true
end

--[[
    Gets the current material configuration for a part

    @param part (BasePart) - The part to check
    @return (string|nil) - The custom material name, or nil if not configured
]]
function MaterialConfig.GetPartMaterial(part)
  if not part or not part:IsA("BasePart") then
    return nil
  end

  return part:GetAttribute("FootstepMaterial")
end

return MaterialConfig
