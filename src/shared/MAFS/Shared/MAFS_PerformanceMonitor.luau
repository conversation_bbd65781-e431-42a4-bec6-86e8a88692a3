--!strict

--[[
 - file: MAFS_PERFORMANCEMONITOR.LUAU

 - version: 2.0.0
 - author: BleckWolf25
 - contributors:

 - copyright: Dynamic Innovative Studio

 - description:
   - Shared module for the Modular Audio FootStep System (MAFS) performance monitoring.
   - Provides comprehensive real-time performance tracking and analytics for system optimization.
   - Monitors sound system efficiency, network performance, material resolution, and resource usage.
   - Implements automatic threshold checking with performance warnings and alerts.
   - Tracks memory usage, frame time, pool efficiency, and validation failure rates.
   - Used by both client and server components for performance analysis and debugging.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- ============================================================================
-- MODULES
-- ============================================================================
local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS_Configuration)

-- ============================================================================
-- CONSTANTS
-- ============================================================================

-- Performance thresholds for warnings and alerts
local PERFORMANCE_THRESHOLDS = {
  MAX_CONCURRENT_SOUNDS = 50, -- Maximum concurrent sounds before warning
  MAX_FRAME_TIME = 0.016, -- 60 FPS target (16.67ms)
  MAX_MEMORY_USAGE = 100 * 1024 * 1024, -- 100 MB memory usage limit
  MIN_POOL_EFFICIENCY = 0.7, -- 70% minimum pool efficiency
  MAX_VALIDATION_FAILURE_RATE = 0.1, -- 10% maximum validation failure rate
}

-- Update frequency for performance monitoring
local UPDATE_INTERVAL = 1.0 -- seconds between performance updates

-- ============================================================================
-- TYPES
-- ============================================================================

-- Sound system metrics structure
type SoundMetrics = {
  totalCreated: number,
  totalDestroyed: number,
  currentActive: number,
  poolHits: number,
  poolMisses: number,
  maxConcurrent: number,
}

-- Network performance metrics structure
type NetworkMetrics = {
  footstepRequests: number,
  footstepBroadcasts: number,
  validationFailures: number,
  bytesTransmitted: number,
}

-- Material system metrics structure
type MaterialMetrics = {
  resolutionAttempts: number,
  customMaterialHits: number,
  robloxMaterialHits: number,
  defaultMaterialHits: number,
  attributeLookups: number,
  raycastOperations: number,
}

-- Performance timing metrics structure
type PerformanceMetrics = {
  averageFrameTime: number,
  peakFrameTime: number,
  memoryUsage: number,
  startTime: number,
  lastUpdateTime: number,
}

-- Error tracking metrics structure
type ErrorMetrics = {
  soundPlaybackFailures: number,
  networkErrors: number,
  materialResolutionErrors: number,
  validationErrors: number,
}

-- Complete performance data structure
type PerformanceData = {
  sounds: SoundMetrics,
  network: NetworkMetrics,
  materials: MaterialMetrics,
  performance: PerformanceMetrics,
  errors: ErrorMetrics,
}

-- Public metrics interface with calculated values
export type PublicSoundMetrics = {
  totalCreated: number,
  totalDestroyed: number,
  currentActive: number,
  maxConcurrent: number,
  poolEfficiency: number,
}

export type PublicNetworkMetrics = {
  footstepRequests: number,
  footstepBroadcasts: number,
  validationFailures: number,
  validationFailureRate: number,
  bytesTransmitted: number,
  requestsPerSecond: number,
}

export type PublicPerformanceMetrics = {
  uptime: number,
  averageFrameTime: number,
  peakFrameTime: number,
  memoryUsage: number,
}

export type PublicMetrics = {
  sounds: PublicSoundMetrics,
  network: PublicNetworkMetrics,
  materials: MaterialMetrics,
  performance: PublicPerformanceMetrics,
  errors: ErrorMetrics,
}

export type PerformanceSummary = {
  uptime: number,
  activeSounds: number,
  totalRequests: number,
  poolEfficiency: number,
  validationFailureRate: number,
  requestsPerSecond: number,
  memoryUsage: number,
  totalErrors: number,
}

-- Module interface for external usage
export type PerformanceMonitorModule = {
  TrackSoundCreated: () -> (),
  TrackSoundDestroyed: () -> (),
  TrackPoolHit: () -> (),
  TrackPoolMiss: () -> (),
  TrackSoundPlaybackFailure: () -> (),
  TrackFootstepRequest: () -> (),
  TrackFootstepBroadcast: (playerCount: number) -> (),
  TrackValidationFailure: () -> (),
  TrackNetworkError: () -> (),
  TrackMaterialResolution: (resolutionType: string) -> (),
  TrackAttributeLookup: () -> (),
  TrackRaycastOperation: () -> (),
  TrackMaterialResolutionError: () -> (),
  CalculatePoolEfficiency: () -> number,
  CalculateValidationFailureRate: () -> number,
  CalculateUptime: () -> number,
  CalculateRequestsPerSecond: () -> number,
  GetMetrics: () -> PublicMetrics,
  GetSummary: () -> PerformanceSummary,
  PrintReport: () -> (),
  Reset: () -> (),
  Initialize: () -> (),
}

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local PerformanceMonitor = {}

-- ============================================================================
-- MODULE STATE
-- ============================================================================

-- Performance tracking data storage
local performanceData: PerformanceData = {
  -- Sound system metrics
  sounds = {
    totalCreated = 0,
    totalDestroyed = 0,
    currentActive = 0,
    poolHits = 0,
    poolMisses = 0,
    maxConcurrent = 0,
  },

  -- Network metrics
  network = {
    footstepRequests = 0,
    footstepBroadcasts = 0,
    validationFailures = 0,
    bytesTransmitted = 0,
  },

  -- Material system metrics
  materials = {
    resolutionAttempts = 0,
    customMaterialHits = 0,
    robloxMaterialHits = 0,
    defaultMaterialHits = 0,
    attributeLookups = 0,
    raycastOperations = 0,
  },

  -- Performance metrics
  performance = {
    averageFrameTime = 0,
    peakFrameTime = 0,
    memoryUsage = 0,
    startTime = tick(),
    lastUpdateTime = tick(),
  },

  -- Error tracking
  errors = {
    soundPlaybackFailures = 0,
    networkErrors = 0,
    materialResolutionErrors = 0,
    validationErrors = 0,
  },
}

-- Last update time for periodic monitoring
local lastUpdateTime = 0

-- ============================================================================
-- SOUND SYSTEM TRACKING FUNCTIONS
-- ============================================================================

--[[
    Tracks the creation of a new sound object

    Updates sound creation metrics and checks for performance warnings
    when concurrent sound count exceeds thresholds.
]]
function PerformanceMonitor.TrackSoundCreated(): ()
  performanceData.sounds.totalCreated = performanceData.sounds.totalCreated + 1
  performanceData.sounds.currentActive = performanceData.sounds.currentActive + 1

  -- Update max concurrent sounds tracking
  if performanceData.sounds.currentActive > performanceData.sounds.maxConcurrent then
    performanceData.sounds.maxConcurrent = performanceData.sounds.currentActive
  end

  -- Check for performance warning when threshold exceeded
  if performanceData.sounds.currentActive > PERFORMANCE_THRESHOLDS.MAX_CONCURRENT_SOUNDS then
    warn(
      string.format(
        "MAFS Performance Warning: %d concurrent sounds (threshold: %d)",
        performanceData.sounds.currentActive,
        PERFORMANCE_THRESHOLDS.MAX_CONCURRENT_SOUNDS
      )
    )
  end
end

--[[
    Tracks the destruction of a sound object

    Updates sound destruction metrics and decrements active sound count.
]]
function PerformanceMonitor.TrackSoundDestroyed(): ()
  performanceData.sounds.totalDestroyed = performanceData.sounds.totalDestroyed + 1
  performanceData.sounds.currentActive = math.max(0, performanceData.sounds.currentActive - 1)
end

--[[
    Tracks a successful sound pool cache hit

    Indicates efficient sound object reuse from the pool.
]]
function PerformanceMonitor.TrackPoolHit(): ()
  performanceData.sounds.poolHits = performanceData.sounds.poolHits + 1
end

--[[
    Tracks a sound pool cache miss

    Indicates a new sound object had to be created instead of reused.
]]
function PerformanceMonitor.TrackPoolMiss(): ()
  performanceData.sounds.poolMisses = performanceData.sounds.poolMisses + 1
end

--[[
    Tracks a sound playback failure

    Records errors in sound playback for debugging and monitoring.
]]
function PerformanceMonitor.TrackSoundPlaybackFailure(): ()
  performanceData.errors.soundPlaybackFailures = performanceData.errors.soundPlaybackFailures + 1
end

-- ============================================================================
-- NETWORK TRACKING FUNCTIONS
-- ============================================================================

--[[
    Tracks a footstep request from client to server

    Records network activity and estimates bandwidth usage.
]]
function PerformanceMonitor.TrackFootstepRequest(): ()
  performanceData.network.footstepRequests = performanceData.network.footstepRequests + 1
  performanceData.network.bytesTransmitted = performanceData.network.bytesTransmitted + 32 -- Approximate request size
end

--[[
    Tracks a footstep broadcast from server to clients

    @param playerCount (number) - Number of players who received the broadcast
]]
function PerformanceMonitor.TrackFootstepBroadcast(playerCount: number): ()
  performanceData.network.footstepBroadcasts = performanceData.network.footstepBroadcasts + 1
  performanceData.network.bytesTransmitted = performanceData.network.bytesTransmitted
    + (playerCount * 64) -- Approximate broadcast size per player
end

--[[
    Tracks a validation failure for anti-exploit protection

    Records both network and error metrics for security monitoring.
]]
function PerformanceMonitor.TrackValidationFailure(): ()
  performanceData.network.validationFailures = performanceData.network.validationFailures + 1
  performanceData.errors.validationErrors = performanceData.errors.validationErrors + 1
end

--[[
    Tracks a general network error

    Records network-related errors for debugging and monitoring.
]]
function PerformanceMonitor.TrackNetworkError(): ()
  performanceData.errors.networkErrors = performanceData.errors.networkErrors + 1
end

-- ============================================================================
-- MATERIAL SYSTEM TRACKING FUNCTIONS
-- ============================================================================

--[[
    Tracks material resolution attempts and their types

    @param resolutionType (string) - Type of material resolution: "custom", "roblox", or "default"
]]
function PerformanceMonitor.TrackMaterialResolution(resolutionType: string): ()
  performanceData.materials.resolutionAttempts = performanceData.materials.resolutionAttempts + 1

  -- Track specific resolution type for analytics
  if resolutionType == "custom" then
    performanceData.materials.customMaterialHits = performanceData.materials.customMaterialHits + 1
  elseif resolutionType == "roblox" then
    performanceData.materials.robloxMaterialHits = performanceData.materials.robloxMaterialHits + 1
  elseif resolutionType == "default" then
    performanceData.materials.defaultMaterialHits = performanceData.materials.defaultMaterialHits
      + 1
  end
end

--[[
    Tracks attribute lookup operations

    Records when the system checks for custom material attributes on parts.
]]
function PerformanceMonitor.TrackAttributeLookup(): ()
  performanceData.materials.attributeLookups = performanceData.materials.attributeLookups + 1
end

--[[
    Tracks raycast operations for material detection

    Records when the system performs raycasting for surface material detection.
]]
function PerformanceMonitor.TrackRaycastOperation(): ()
  performanceData.materials.raycastOperations = performanceData.materials.raycastOperations + 1
end

--[[
    Tracks material resolution errors

    Records failures in material detection and resolution.
]]
function PerformanceMonitor.TrackMaterialResolutionError(): ()
  performanceData.errors.materialResolutionErrors = performanceData.errors.materialResolutionErrors
    + 1
end

-- ============================================================================
-- PERFORMANCE ANALYSIS FUNCTIONS
-- ============================================================================

--[[
    Calculates the efficiency of the sound object pool

    @return (number) - Pool efficiency as a ratio (0.0 to 1.0)
]]
function PerformanceMonitor.CalculatePoolEfficiency(): number
  local totalPoolOperations = performanceData.sounds.poolHits + performanceData.sounds.poolMisses
  if totalPoolOperations == 0 then
    return 1.0 -- Perfect efficiency when no operations have occurred
  end
  return performanceData.sounds.poolHits / totalPoolOperations
end

--[[
    Calculates the rate of validation failures

    @return (number) - Validation failure rate as a ratio (0.0 to 1.0)
]]
function PerformanceMonitor.CalculateValidationFailureRate(): number
  local totalRequests = performanceData.network.footstepRequests
  if totalRequests == 0 then
    return 0.0 -- No failures when no requests
  end
  return performanceData.network.validationFailures / totalRequests
end

--[[
    Calculates the system uptime in seconds

    @return (number) - Uptime in seconds since initialization
]]
function PerformanceMonitor.CalculateUptime(): number
  return tick() - performanceData.performance.startTime
end

--[[
    Calculates the average requests per second

    @return (number) - Average footstep requests per second
]]
function PerformanceMonitor.CalculateRequestsPerSecond(): number
  local uptime = PerformanceMonitor.CalculateUptime()
  if uptime == 0 then
    return 0 -- Avoid division by zero
  end
  return performanceData.network.footstepRequests / uptime
end

-- ============================================================================
-- PERFORMANCE MONITORING FUNCTIONS
-- ============================================================================

--[[
    Updates performance metrics and checks thresholds

    Called periodically to track frame time, memory usage, and trigger warnings.
]]
function PerformanceMonitor.Update(): ()
  local currentTime = tick()

  -- Update frame time tracking
  local deltaTime = currentTime - performanceData.performance.lastUpdateTime
  performanceData.performance.lastUpdateTime = currentTime

  if deltaTime > 0 then
    -- Update average frame time using exponential moving average
    performanceData.performance.averageFrameTime = (
      performanceData.performance.averageFrameTime * 0.9
    ) + (deltaTime * 0.1)

    -- Update peak frame time if current frame is slower
    if deltaTime > performanceData.performance.peakFrameTime then
      performanceData.performance.peakFrameTime = deltaTime
    end
  end

  -- Update memory usage (convert from KB to bytes)
  performanceData.performance.memoryUsage = collectgarbage("count") * 1024

  -- Check performance thresholds and trigger warnings if needed
  PerformanceMonitor.CheckPerformanceThresholds()
end

--[[
    Checks performance thresholds and issues warnings

    Monitors various performance metrics and warns when thresholds are exceeded.
]]
function PerformanceMonitor.CheckPerformanceThresholds(): ()
  -- Check sound pool efficiency
  local poolEfficiency = PerformanceMonitor.CalculatePoolEfficiency()
  if poolEfficiency < PERFORMANCE_THRESHOLDS.MIN_POOL_EFFICIENCY then
    warn(
      string.format(
        "MAFS Performance Warning: Low pool efficiency %.1f%% (threshold: %.1f%%)",
        poolEfficiency * 100,
        PERFORMANCE_THRESHOLDS.MIN_POOL_EFFICIENCY * 100
      )
    )
  end

  -- Check validation failure rate for security concerns
  local failureRate = PerformanceMonitor.CalculateValidationFailureRate()
  if failureRate > PERFORMANCE_THRESHOLDS.MAX_VALIDATION_FAILURE_RATE then
    warn(
      string.format(
        "MAFS Performance Warning: High validation failure rate %.1f%% (threshold: %.1f%%)",
        failureRate * 100,
        PERFORMANCE_THRESHOLDS.MAX_VALIDATION_FAILURE_RATE * 100
      )
    )
  end

  -- Check frame time performance
  if performanceData.performance.averageFrameTime > PERFORMANCE_THRESHOLDS.MAX_FRAME_TIME then
    warn(
      string.format(
        "MAFS Performance Warning: High frame time %.3fs (threshold: %.3fs)",
        performanceData.performance.averageFrameTime,
        PERFORMANCE_THRESHOLDS.MAX_FRAME_TIME
      )
    )
  end

  -- Check memory usage
  if performanceData.performance.memoryUsage > PERFORMANCE_THRESHOLDS.MAX_MEMORY_USAGE then
    warn(
      string.format(
        "MAFS Performance Warning: High memory usage %.1f MB (threshold: %.1f MB)",
        performanceData.performance.memoryUsage / (1024 * 1024),
        PERFORMANCE_THRESHOLDS.MAX_MEMORY_USAGE / (1024 * 1024)
      )
    )
  end
end

-- ============================================================================
-- PUBLIC API FUNCTIONS
-- ============================================================================

--[[
    Gets comprehensive performance metrics

    @return (PublicMetrics) - Complete performance data with calculated values
]]
function PerformanceMonitor.GetMetrics(): PublicMetrics
  return {
    sounds = {
      totalCreated = performanceData.sounds.totalCreated,
      totalDestroyed = performanceData.sounds.totalDestroyed,
      currentActive = performanceData.sounds.currentActive,
      maxConcurrent = performanceData.sounds.maxConcurrent,
      poolEfficiency = PerformanceMonitor.CalculatePoolEfficiency(),
    },

    network = {
      footstepRequests = performanceData.network.footstepRequests,
      footstepBroadcasts = performanceData.network.footstepBroadcasts,
      validationFailures = performanceData.network.validationFailures,
      validationFailureRate = PerformanceMonitor.CalculateValidationFailureRate(),
      bytesTransmitted = performanceData.network.bytesTransmitted,
      requestsPerSecond = PerformanceMonitor.CalculateRequestsPerSecond(),
    },

    materials = {
      resolutionAttempts = performanceData.materials.resolutionAttempts,
      customMaterialHits = performanceData.materials.customMaterialHits,
      robloxMaterialHits = performanceData.materials.robloxMaterialHits,
      defaultMaterialHits = performanceData.materials.defaultMaterialHits,
      attributeLookups = performanceData.materials.attributeLookups,
      raycastOperations = performanceData.materials.raycastOperations,
    },

    performance = {
      uptime = PerformanceMonitor.CalculateUptime(),
      averageFrameTime = performanceData.performance.averageFrameTime,
      peakFrameTime = performanceData.performance.peakFrameTime,
      memoryUsage = performanceData.performance.memoryUsage,
    },

    errors = {
      soundPlaybackFailures = performanceData.errors.soundPlaybackFailures,
      networkErrors = performanceData.errors.networkErrors,
      materialResolutionErrors = performanceData.errors.materialResolutionErrors,
      validationErrors = performanceData.errors.validationErrors,
    },
  }
end

--[[
    Gets a summary of key performance metrics

    @return (PerformanceSummary) - Condensed performance overview
]]
function PerformanceMonitor.GetSummary(): PerformanceSummary
  local metrics = PerformanceMonitor.GetMetrics()

  return {
    uptime = metrics.performance.uptime,
    activeSounds = metrics.sounds.currentActive,
    totalRequests = metrics.network.footstepRequests,
    poolEfficiency = metrics.sounds.poolEfficiency,
    validationFailureRate = metrics.network.validationFailureRate,
    requestsPerSecond = metrics.network.requestsPerSecond,
    memoryUsage = metrics.performance.memoryUsage,
    totalErrors = metrics.errors.soundPlaybackFailures
      + metrics.errors.networkErrors
      + metrics.errors.materialResolutionErrors
      + metrics.errors.validationErrors,
  }
end

--[[
    Prints a detailed performance report to the console

    Outputs comprehensive performance metrics in a human-readable format.
]]
function PerformanceMonitor.PrintReport(): ()
  local metrics = PerformanceMonitor.GetMetrics()

  print("=== MAFS Performance Report ===")
  print(string.format("Uptime: %.1f seconds", metrics.performance.uptime))
  print("")

  print("Sound System:")
  print(
    string.format(
      "  Active Sounds: %d (Max: %d)",
      metrics.sounds.currentActive,
      metrics.sounds.maxConcurrent
    )
  )
  print(string.format("  Total Created: %d", metrics.sounds.totalCreated))
  print(string.format("  Pool Efficiency: %.1f%%", metrics.sounds.poolEfficiency * 100))
  print("")

  print("Network:")
  print(string.format("  Footstep Requests: %d", metrics.network.footstepRequests))
  print(string.format("  Requests/Second: %.1f", metrics.network.requestsPerSecond))
  print(
    string.format(
      "  Validation Failures: %d (%.1f%%)",
      metrics.network.validationFailures,
      metrics.network.validationFailureRate * 100
    )
  )
  print(string.format("  Bytes Transmitted: %.1f KB", metrics.network.bytesTransmitted / 1024))
  print("")

  print("Materials:")
  print(string.format("  Resolution Attempts: %d", metrics.materials.resolutionAttempts))
  print(string.format("  Custom Material Hits: %d", metrics.materials.customMaterialHits))
  print(string.format("  Roblox Material Hits: %d", metrics.materials.robloxMaterialHits))
  print(string.format("  Default Material Hits: %d", metrics.materials.defaultMaterialHits))
  print("")

  print("Performance:")
  print(string.format("  Average Frame Time: %.3f ms", metrics.performance.averageFrameTime * 1000))
  print(string.format("  Peak Frame Time: %.3f ms", metrics.performance.peakFrameTime * 1000))
  print(string.format("  Memory Usage: %.1f MB", metrics.performance.memoryUsage / (1024 * 1024)))
  print("")

  local totalErrors = metrics.errors.soundPlaybackFailures
    + metrics.errors.networkErrors
    + metrics.errors.materialResolutionErrors
    + metrics.errors.validationErrors
  print(string.format("Total Errors: %d", totalErrors))
  if totalErrors > 0 then
    print(string.format("  Sound Playback Failures: %d", metrics.errors.soundPlaybackFailures))
    print(string.format("  Network Errors: %d", metrics.errors.networkErrors))
    print(
      string.format("  Material Resolution Errors: %d", metrics.errors.materialResolutionErrors)
    )
    print(string.format("  Validation Errors: %d", metrics.errors.validationErrors))
  end
end

--[[
    Resets all performance metrics to initial values

    Useful for testing or when restarting the monitoring system.
]]
function PerformanceMonitor.Reset(): ()
  performanceData = {
    sounds = {
      totalCreated = 0,
      totalDestroyed = 0,
      currentActive = 0,
      poolHits = 0,
      poolMisses = 0,
      maxConcurrent = 0,
    },
    network = {
      footstepRequests = 0,
      footstepBroadcasts = 0,
      validationFailures = 0,
      bytesTransmitted = 0,
    },
    materials = {
      resolutionAttempts = 0,
      customMaterialHits = 0,
      robloxMaterialHits = 0,
      defaultMaterialHits = 0,
      attributeLookups = 0,
      raycastOperations = 0,
    },
    performance = {
      averageFrameTime = 0,
      peakFrameTime = 0,
      memoryUsage = 0,
      startTime = tick(),
      lastUpdateTime = tick(),
    },
    errors = {
      soundPlaybackFailures = 0,
      networkErrors = 0,
      materialResolutionErrors = 0,
      validationErrors = 0,
    },
  }

  -- Reset the last update time
  lastUpdateTime = 0

  print("MAFS: Performance metrics reset")
end

-- ============================================================================
-- INITIALIZATION FUNCTIONS
-- ============================================================================

--[[
    Initializes the performance monitoring system

    Sets up periodic updates and connects to the RunService heartbeat.
]]
function PerformanceMonitor.Initialize(): ()
  if MAFSConfig.Settings.EnablePerformanceMetrics then
    -- Set up periodic updates using RunService heartbeat
    RunService.Heartbeat:Connect(function()
      local currentTime = tick()
      if currentTime - lastUpdateTime >= UPDATE_INTERVAL then
        PerformanceMonitor.Update()
        lastUpdateTime = currentTime
      end
    end)

    print("MAFS: Performance monitoring initialized successfully")
  else
    print("MAFS: Performance monitoring disabled in configuration")
  end
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return PerformanceMonitor :: PerformanceMonitorModule
