--!strict

--[[
 - file: MAFS_API.LUAU

 - version: 2.1.0
 - author: BleckWolf25
 - contributors:

 - copyright: Dynamic Innovative Studio

 - description:
   - Public API module for the Modular Audio FootStep System (MAFS).
   - Provides clean, type-safe interface for external systems and frameworks integration.
   - Abstracts internal complexity while exposing essential functionality for material configuration.
   - Supports both client and server operations with automatic environment detection.
   - Enables integration with gun systems, character frameworks, and other external systems.
   - Includes comprehensive error handling, validation, and performance monitoring access.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- ============================================================================
-- MODULES
-- ============================================================================
local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS_Configuration)
local MAFSUtils =
  require(ReplicatedStorage:WaitForChild("MAFS"):WaitForChild("Shared"):WaitForChild("MAFS_Utils"))
local MaterialConfig = require(
  ReplicatedStorage:WaitForChild("MAFS")
    :WaitForChild("Shared")
    :WaitForChild("MAFS_Material_Configuration")
)
local PerformanceMonitor = require(
  ReplicatedStorage:WaitForChild("MAFS")
    :WaitForChild("Shared")
    :WaitForChild("MAFS_PerformanceMonitor")
)

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local API_VERSION = "2.1.0" -- Current API version

-- ============================================================================
-- TYPES
-- ============================================================================

-- System information structure
type SystemInfo = {
  Version: string,
  DebugMode: boolean,
  AvailableMaterials: { string },
  Settings: {
    BroadcastRadius: number,
    ServerCooldown: number,
    ClientCooldown: number,
    MovementThreshold: number,
  },
}

-- Performance metrics from PerformanceMonitor
type PerformanceMetrics = PerformanceMonitor.PublicMetrics

-- Module interface for external usage
export type MAFSAPIModule = {
  -- Material Configuration API
  SetPartMaterial: (part: BasePart, materialName: string) -> boolean,
  SetModelMaterial: (model: Instance, materialName: string) -> number,
  SetPartsByPattern: (parent: Instance, namePattern: string, materialName: string) -> number,
  SetPartsByMaterial: (
    parent: Instance,
    robloxMaterial: Enum.Material,
    materialName: string
  ) -> number,
  ClearPartMaterial: (part: BasePart) -> boolean,
  GetPartMaterial: (part: BasePart) -> string?,

  -- System Control API (Client Only)
  SetVolume: (volume: number) -> boolean,
  SetEnabled: (enabled: boolean) -> boolean,

  -- System Information API
  GetAvailableMaterials: () -> { string },
  IsValidMaterial: (materialName: string) -> boolean,
  GetSystemInfo: () -> SystemInfo,

  -- Debug and Monitoring API
  SetDebugMode: (enabled: boolean) -> (),
  IsDebugMode: () -> boolean,
  GetPerformanceMetrics: () -> PerformanceMetrics?,
}

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local MAFSAPI = {}

-- ============================================================================
-- MODULE STATE
-- ============================================================================

-- Client-only modules (loaded conditionally for safety)
local FootstepClient: any = nil
if RunService:IsClient() then
  local success, module = pcall(function()
    return require(script.Parent.MAFS.MAFS_Client_Module)
  end)
  if success then
    FootstepClient = module
  end
end

-- ============================================================================
-- MATERIAL CONFIGURATION API
-- ============================================================================

--[[
    Sets a custom footstep material for a single part

    @param part (BasePart) - The part to configure
    @param materialName (string) - The custom material name ("Snow", "Gravel", etc.)
    @return (boolean) - Success status

    Example:
        MAFSAPI.SetPartMaterial(workspace.SnowPile, "Snow")
]]
function MAFSAPI.SetPartMaterial(part: BasePart, materialName: string): boolean
  return MaterialConfig.ConfigurePart(part, materialName)
end

--[[
    Sets a custom footstep material for all parts in a model

    @param model (Instance) - The model containing parts to configure
    @param materialName (string) - The custom material name
    @return (number) - Number of parts configured

    Example:
        MAFSAPI.SetModelMaterial(workspace.WoodenBridge, "Wood")
]]
function MAFSAPI.SetModelMaterial(model: Instance, materialName: string): number
  return MaterialConfig.ConfigureModel(model, materialName)
end

--[[
    Sets custom footstep materials for parts matching a name pattern

    @param parent (Instance) - The parent to search
    @param namePattern (string) - Lua pattern to match part names
    @param materialName (string) - The custom material name
    @return (number) - Number of parts configured

    Example:
        MAFSAPI.SetPartsByPattern(workspace, "^Metal_", "MetalGrate")
]]
function MAFSAPI.SetPartsByPattern(
  parent: Instance,
  namePattern: string,
  materialName: string
): number
  return MaterialConfig.ConfigureByNamePattern(parent, namePattern, materialName)
end

--[[
    Sets custom footstep materials for parts with specific Roblox material

    @param parent (Instance) - The parent to search
    @param robloxMaterial (Enum.Material) - The Roblox material to match
    @param materialName (string) - The custom material name
    @return (number) - Number of parts configured

    Example:
        MAFSAPI.SetPartsByMaterial(workspace, Enum.Material.Sand, "DeepSand")
]]
function MAFSAPI.SetPartsByMaterial(
  parent: Instance,
  robloxMaterial: Enum.Material,
  materialName: string
): number
  return MaterialConfig.ConfigureByRobloxMaterial(parent, robloxMaterial, materialName)
end

--[[
    Clears custom material configuration from a part

    @param part (BasePart) - The part to clear
    @return (boolean) - Success status

    Example:
        MAFSAPI.ClearPartMaterial(workspace.SomePart)
]]
function MAFSAPI.ClearPartMaterial(part: BasePart): boolean
  return MaterialConfig.ClearPartMaterial(part)
end

--[[
    Gets the current material configuration for a part

    @param part (BasePart) - The part to check
    @return (string?) - The custom material name, or nil if not configured

    Example:
        local material = MAFSAPI.GetPartMaterial(workspace.SomePart)
        if material then
            print("Part has custom material:", material)
        end
]]
function MAFSAPI.GetPartMaterial(part: BasePart): string?
  return MaterialConfig.GetPartMaterial(part)
end

-- ============================================================================
-- SYSTEM CONTROL API (CLIENT ONLY)
-- ============================================================================

--[[
    Sets the volume for all footstep sounds (Client only)

    @param volume (number) - Volume level (0-1)
    @return (boolean) - Success status

    Example:
        MAFSAPI.SetVolume(0.8)
]]
function MAFSAPI.SetVolume(volume: number): boolean
  if not RunService:IsClient() then
    warn("MAFS: SetVolume can only be called on the client")
    return false
  end

  if not FootstepClient then
    warn("MAFS: FootstepClient not available")
    return false
  end

  FootstepClient.SetVolume(volume)
  return true
end

--[[
    Enables or disables footstep sound playback (Client only)

    @param enabled (boolean) - Whether footsteps should be played
    @return (boolean) - Success status

    Example:
        MAFSAPI.SetEnabled(false) -- Disable for cutscenes
        MAFSAPI.SetEnabled(true)  -- Re-enable
]]
function MAFSAPI.SetEnabled(enabled: boolean): boolean
  if not RunService:IsClient() then
    warn("MAFS: SetEnabled can only be called on the client")
    return false
  end

  if not FootstepClient then
    warn("MAFS: FootstepClient not available")
    return false
  end

  FootstepClient.SetEnabled(enabled)
  return true
end

-- ============================================================================
-- SYSTEM INFORMATION API
-- ============================================================================

--[[
    Gets the list of available custom materials

    @return ({ string }) - Array of custom material names

    Example:
        local materials = MAFSAPI.GetAvailableMaterials()
        for _, material in ipairs(materials) do
            print("Available material:", material)
        end
]]
function MAFSAPI.GetAvailableMaterials(): { string }
  local materials: { string } = {}
  for materialName, _ in MAFSConfig.CustomMaterials do
    table.insert(materials, materialName)
  end
  return materials
end

--[[
    Checks if a material name is valid

    @param materialName (string) - The material name to validate
    @return (boolean) - Whether the material is valid

    Example:
        if MAFSAPI.IsValidMaterial("Snow") then
            print("Snow is a valid material")
        end
]]
function MAFSAPI.IsValidMaterial(materialName: string): boolean
  return MAFSUtils.ValidateMaterialName(materialName)
end

--[[
    Gets system configuration information

    @return (SystemInfo) - System configuration data including version, settings, and available materials

    Example:
        local info = MAFSAPI.GetSystemInfo()
        print("MAFS Version:", info.Version)
        print("Debug Mode:", info.DebugMode)
]]
function MAFSAPI.GetSystemInfo(): SystemInfo
  return {
    Version = API_VERSION,
    DebugMode = MAFSConfig.IsDebugMode(),
    AvailableMaterials = MAFSAPI.GetAvailableMaterials(),
    Settings = {
      BroadcastRadius = MAFSConfig.Settings.BroadcastRadius,
      ServerCooldown = MAFSConfig.Settings.ServerCooldown,
      ClientCooldown = MAFSConfig.Settings.ClientCooldown,
      MovementThreshold = MAFSConfig.Settings.MovementThreshold,
    },
  }
end

-- ============================================================================
-- DEBUG AND MONITORING API
-- ============================================================================

--[[
    Enables or disables debug mode

    @param enabled (boolean) - Whether debug mode should be enabled

    Example:
        MAFSAPI.SetDebugMode(true) -- Enable debug logging
]]
function MAFSAPI.SetDebugMode(enabled: boolean): ()
  MAFSConfig.SetDebugMode(enabled)
end

--[[
    Gets the current debug mode status

    @return (boolean) - Whether debug mode is enabled

    Example:
        if MAFSAPI.IsDebugMode() then
            print("Debug mode is currently enabled")
        end
]]
function MAFSAPI.IsDebugMode(): boolean
  return MAFSConfig.IsDebugMode()
end

--[[
    Gets performance metrics (if available)

    @return (PerformanceMetrics?) - Performance metrics or nil if not available

    Example:
        local metrics = MAFSAPI.GetPerformanceMetrics()
        if metrics then
            print("Active sounds:", metrics.sounds.currentActive)
            print("Pool efficiency:", metrics.sounds.poolEfficiency)
        end
]]
function MAFSAPI.GetPerformanceMetrics(): PerformanceMetrics?
  local success, metrics = pcall(function()
    return PerformanceMonitor.GetMetrics()
  end)

  if success then
    return metrics
  else
    warn("MAFS: Failed to get performance metrics:", metrics)
    return nil
  end
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return MAFSAPI :: MAFSAPIModule
