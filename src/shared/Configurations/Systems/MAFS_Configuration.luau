--!strict

--[[
    - file: MAFS_Configuration.luau

    - version: 2.0.0
    - author: B<PERSON><PERSON>Wolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Central configuration module for the MAFS (Modular Audio FootStep) system.
      - Centralized material sound definitions and properties
      - System-wide settings and performance parameters
      - Debug mode and monitoring configuration
      - Security and rate limiting parameters
      - Cross-platform compatibility (client/server)

    - architecture:
    This module follows a declarative configuration pattern where all system
    behavior is defined through data structures rather than imperative code.
    It serves as the single source of truth for all MAFS system parameters.

    - usage:
    local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS_Configuration)

    -- Get material data for a specific material
    local grassData = MAFSConfig.GetMaterialData(Enum.Material.Grass)

    -- Get a random sound for the material
    local soundId = MAFSConfig.GetRandomSoundId(grassData)

    -- Check if debug mode is enabled
    if MAFSConfig.IsDebugMode() then
        print("Debug mode active")
    end

    - performance:
      - All material lookups are O(1) hash table operations
      - Sound selection uses optimized random number generation
      - Configuration is loaded once and cached for the session

    - security:
      - All configuration values are validated at runtime
      - Rate limiting parameters prevent abuse
      - Anti-teleport protection through distance validation

    - note: To configure the MAFS system, modify the Settings table below.
          Search for "MAFSConfig.Settings = {" to find configuration options.
]]

-- ============================================================================
-- TYPE ANNOTATIONS & DEFINITIONS
-- ============================================================================

-- Configuration settings type
export type MAFSSettings = {
  -- Debug and monitoring
  DebugMode: boolean,
  EnablePerformanceMetrics: boolean,

  -- Performance and resource management
  MaxCachedSounds: number,
  BroadcastRadius: number,

  -- Rate limiting and security
  ServerCooldown: number,
  ClientCooldown: number,
  MaxDistanceDelta: number,

  -- Audio timing and sensitivity
  MovementThreshold: number,
  StepInterval: number,
}

-- Playback speed range tuple
export type PlaybackSpeedRange = { number }

-- Material data structure
export type MaterialData = {
  SoundIds: { string },
  Volume: number,
  PlaybackSpeedRange: PlaybackSpeedRange,
  RollOffMinDistance: number,
  RollOffMaxDistance: number,
}

-- Material identifier (either built-in enum or custom string)
export type MaterialIdentifier = Enum.Material | string | nil

-- Built-in materials mapping
export type MaterialSoundsMap = { [Enum.Material]: MaterialData }

-- Custom materials mapping
export type CustomMaterialsMap = { [string]: MaterialData }

-- Main configuration module type
export type MAFSConfigModule = {
  Settings: MAFSSettings,
  MaterialSounds: MaterialSoundsMap,
  CustomMaterials: CustomMaterialsMap,
  DefaultMaterial: MaterialData,

  -- Core utility functions
  GetMaterialData: (material: MaterialIdentifier) -> MaterialData,
  GetRandomSoundId: (materialData: MaterialData) -> string,
  GetRandomPlaybackSpeed: (materialData: MaterialData) -> number,
  IsDebugMode: () -> boolean,
  SetDebugMode: (enabled: boolean) -> (),
  ValidateConfiguration: () -> boolean,
  ValidateMaterialData: (data: MaterialData, name: string) -> boolean,
  GetAvailableMaterials: () -> { string },
}

-- ============================================================================
-- MODULE DEFINITION
-- ============================================================================
local MAFSConfig: MAFSConfigModule = {} :: MAFSConfigModule

-- ============================================================================
-- MAFS SYSTEM SETTINGS
-- ============================================================================

--[[
    Core system configuration settings

    PERFORMANCE IMPACT:
    - BroadcastRadius: Higher values increase network traffic
    - MaxCachedSounds: Higher values use more memory but reduce GC pressure
    - Cooldowns: Lower values increase CPU usage but improve responsiveness

    SECURITY CONSIDERATIONS:
    - ServerCooldown: Prevents request spam and reduces server load
    - ClientCooldown: Should be slightly higher than server to prevent race conditions
    - MaxDistanceDelta: Prevents teleportation exploits
    - MovementThreshold: Prevents micro-movement spam

    AUDIO QUALITY:
    - StepInterval: Lower values create more realistic footsteps but use more resources
    - MovementThreshold: Lower values are more sensitive but may trigger false positives
]]
MAFSConfig.Settings = {
  -- Debug and monitoring settings
  DebugMode = false, -- Enable debug logging and diagnostics
  EnablePerformanceMetrics = true, -- Track system performance metrics

  -- Performance and resource management
  MaxCachedSounds = 20, -- Maximum sounds in object pool (memory vs GC trade-off)
  BroadcastRadius = 50, -- Maximum distance for footstep broadcasting (studs)

  -- Rate limiting and security
  ServerCooldown = 0.25, -- Minimum time between server validations (seconds)
  ClientCooldown = 0.27, -- Minimum time between client requests (slightly higher than server)
  MaxDistanceDelta = 10, -- Maximum movement distance per frame (anti-teleport protection)

  -- Audio timing and sensitivity
  MovementThreshold = 0.1, -- Minimum movement distance to trigger footstep (studs)
  StepInterval = 0.3, -- Minimum time between footstep sounds (seconds)
}

-- ============================================================================
-- BUILT-IN MATERIAL SOUND DEFINITIONS
-- ============================================================================

--[[
    Sound configuration for built-in Roblox materials

    AUDIO DESIGN PRINCIPLES:
    - Volume: Reflects material density and hardness (0.5-1.2 range)
    - PlaybackSpeedRange: Adds natural variation (typically ±10-20%)
    - RollOffDistance: Matches real-world sound propagation characteristics

    PERFORMANCE NOTES:
    - Multiple SoundIds provide variation and prevent repetition
    - RollOff distances are optimized for typical game environments
    - Volume levels are balanced for consistent player experience

    CUSTOMIZATION:
    - Add more SoundIds for greater variation
    - Adjust Volume for game-specific balance
    - Modify RollOff distances based on map scale
]]
MAFSConfig.MaterialSounds = {
  -- Natural materials
  [Enum.Material.Grass] = {
    SoundIds = {
      "rbxassetid://9118663153", -- Soft grass step 1
      "rbxassetid://9118663295", -- Soft grass step 2
      "rbxassetid://9118663485", -- Soft grass step 3
    },
    Volume = 0.8, -- Moderate volume for natural feel
    PlaybackSpeedRange = { 0.9, 1.1 }, -- ±10% speed variation
    RollOffMinDistance = 5, -- Close proximity for intimate sound
    RollOffMaxDistance = 50, -- Standard outdoor range
  },

  [Enum.Material.Sand] = {
    SoundIds = {
      "rbxassetid://9118663775", -- Sand crunch 1
      "rbxassetid://9118664041", -- Sand crunch 2
      "rbxassetid://9118664270", -- Sand crunch 3
    },
    Volume = 0.7, -- Softer than grass due to absorption
    PlaybackSpeedRange = { 0.9, 1.1 }, -- Natural variation
    RollOffMinDistance = 4, -- Closer due to muffled nature
    RollOffMaxDistance = 45, -- Slightly reduced range
  },

  -- Hard surfaces
  [Enum.Material.Concrete] = {
    SoundIds = {
      "rbxassetid://9118662067", -- Hard concrete step 1
      "rbxassetid://9118662323", -- Hard concrete step 2
      "rbxassetid://9118662536", -- Hard concrete step 3
    },
    Volume = 1.0, -- Standard reference volume
    PlaybackSpeedRange = { 0.95, 1.05 }, -- Tighter range for consistent feel
    RollOffMinDistance = 6, -- Slightly further due to hardness
    RollOffMaxDistance = 55, -- Good propagation on hard surface
  },

  [Enum.Material.Wood] = {
    SoundIds = {
      "rbxassetid://9118664500", -- Wood creak 1
      "rbxassetid://9118664650", -- Wood creak 2
      "rbxassetid://9118664800", -- Wood creak 3
    },
    Volume = 0.9, -- Slightly softer than concrete
    PlaybackSpeedRange = { 0.9, 1.1 }, -- Natural wood variation
    RollOffMinDistance = 5, -- Moderate proximity
    RollOffMaxDistance = 50, -- Standard indoor/outdoor range
  },

  -- Metallic materials
  [Enum.Material.Metal] = {
    SoundIds = {
      "rbxassetid://9118665000", -- Metal clang 1
      "rbxassetid://9118665150", -- Metal clang 2
      "rbxassetid://9118665300", -- Metal clang 3
    },
    Volume = 1.1, -- Louder due to resonance
    PlaybackSpeedRange = { 0.95, 1.05 }, -- Tighter for metallic consistency
    RollOffMinDistance = 7, -- Further due to sharp sound
    RollOffMaxDistance = 60, -- Excellent propagation
  },

  [Enum.Material.Rock] = {
    SoundIds = {
      "rbxassetid://9118665500", -- Rock scrape 1
      "rbxassetid://9118665650", -- Rock scrape 2
      "rbxassetid://9118665800", -- Rock scrape 3
    },
    Volume = 1.0, -- Standard hard surface volume
    PlaybackSpeedRange = { 0.9, 1.1 }, -- Natural stone variation
    RollOffMinDistance = 6, -- Similar to concrete
    RollOffMaxDistance = 55, -- Good outdoor propagation
  },

  -- Special materials
  [Enum.Material.Water] = {
    SoundIds = {
      "rbxassetid://9118666000", -- Water splash 1
      "rbxassetid://9118666150", -- Water splash 2
      "rbxassetid://9118666300", -- Water splash 3
    },
    Volume = 0.6, -- Quieter due to absorption
    PlaybackSpeedRange = { 0.8, 1.2 }, -- Wider range for fluid dynamics
    RollOffMinDistance = 4, -- Close due to muffled nature
    RollOffMaxDistance = 40, -- Reduced range in water
  },
}

-- ============================================================================
-- CUSTOM MATERIAL SOUND DEFINITIONS
-- ============================================================================

--[[
    Custom materials accessed via FootstepMaterial attribute on parts

    USAGE:
    part:SetAttribute("FootstepMaterial", "Snow")

    NAMING CONVENTIONS:
    - Use PascalCase for consistency (e.g., "MetalGrate", not "metal_grate")
    - Names should be descriptive and unique
    - Avoid conflicts with built-in material names

    IMPLEMENTATION NOTES:
    - Custom materials override built-in material detection
    - Attribute-based system allows per-part customization
    - Supports hierarchical material assignment (model-wide)

    EXTENSIBILITY:
    - Add new materials by following the existing pattern
    - Ensure all required fields are present
    - Test audio balance with existing materials
]]
MAFSConfig.CustomMaterials = {
  -- Weather and environmental materials
  ["Snow"] = {
    SoundIds = {
      "rbxassetid://9118666500", -- Soft snow crunch 1
      "rbxassetid://9118666650", -- Soft snow crunch 2
      "rbxassetid://9118666800", -- Soft snow crunch 3
    },
    Volume = 0.5, -- Very quiet due to snow absorption
    PlaybackSpeedRange = { 0.8, 1.0 }, -- Slower range for muffled effect
    RollOffMinDistance = 3, -- Very close due to absorption
    RollOffMaxDistance = 35, -- Reduced range in snow
  },

  ["Gravel"] = {
    SoundIds = {
      "rbxassetid://9118667000", -- Gravel crunch 1
      "rbxassetid://9118667150", -- Gravel crunch 2
      "rbxassetid://9118667300", -- Gravel crunch 3
    },
    Volume = 0.9, -- Moderate volume with texture
    PlaybackSpeedRange = { 0.9, 1.1 }, -- Natural variation for loose material
    RollOffMinDistance = 5, -- Standard proximity
    RollOffMaxDistance = 50, -- Good outdoor propagation
  },

  ["Mud"] = {
    SoundIds = {
      "rbxassetid://9118667500", -- Wet mud squelch 1
      "rbxassetid://9118667650", -- Wet mud squelch 2
      "rbxassetid://9118667800", -- Wet mud squelch 3
    },
    Volume = 0.7, -- Muffled by moisture
    PlaybackSpeedRange = { 0.8, 1.0 }, -- Slower for viscous effect
    RollOffMinDistance = 4, -- Close due to absorption
    RollOffMaxDistance = 40, -- Reduced range due to dampening
  },

  -- Industrial and structural materials
  ["MetalGrate"] = {
    SoundIds = {
      "rbxassetid://9118668000", -- Metal grate clang 1
      "rbxassetid://9118668150", -- Metal grate clang 2
      "rbxassetid://9118668300", -- Metal grate clang 3
    },
    Volume = 1.2, -- Louder due to resonance and echo
    PlaybackSpeedRange = { 0.95, 1.05 }, -- Tight range for metallic consistency
    RollOffMinDistance = 8, -- Further due to sharp, penetrating sound
    RollOffMaxDistance = 65, -- Excellent propagation through structure
  },
}

-- ============================================================================
-- DEFAULT MATERIAL CONFIGURATION
-- ============================================================================

--[[
    Fallback material used when no specific material is found

    DESIGN RATIONALE:
    - Uses concrete sounds as a neutral, common baseline
    - Moderate volume and standard rolloff for general compatibility
    - Conservative settings to avoid audio issues

    USAGE:
    - Automatically used when material resolution fails
    - Serves as a safe fallback for unknown materials
    - Should represent the most common surface type in your game
]]
MAFSConfig.DefaultMaterial = {
  SoundIds = {
    "rbxassetid://9118662067", -- Generic concrete step 1
    "rbxassetid://9118662323", -- Generic concrete step 2
  },
  Volume = 0.8, -- Safe moderate volume
  PlaybackSpeedRange = { 0.9, 1.1 }, -- Standard variation
  RollOffMinDistance = 5, -- Standard proximity
  RollOffMaxDistance = 50, -- Standard range
}

local CONSTANTS = {
  MIN_VOLUME = 0,
  MAX_VOLUME = 2,
  PLAYBACK_SPEED_RANGE_SIZE = 2,
  MIN_SOUND_IDS = 1,
} :: {
  MIN_VOLUME: number,
  MAX_VOLUME: number,
  PLAYBACK_SPEED_RANGE_SIZE: number,
  MIN_SOUND_IDS: number,
}

-- ============================================================================
-- CORE UTILITY FUNCTIONS
-- ============================================================================

--[[
    Retrieves material data with hierarchical fallback system

    RESOLUTION ORDER:
    1. Built-in Roblox material (if Enum.Material provided)
    2. Custom material (if string provided and exists)
    3. Default material (fallback)

    PARAMETERS:
    material: MaterialIdentifier - Either Enum.Material or string

    RETURNS:
    MaterialData - Complete material configuration

    PERFORMANCE:
    - O(1) hash table lookups
    - No iteration or complex logic
    - Optimized for frequent calls during gameplay
]]
function MAFSConfig.GetMaterialData(material: MaterialIdentifier): MaterialData
  -- Type guard for material parameter
  if material == nil then
    warn("MAFS: GetMaterialData received nil material, using default")
    return MAFSConfig.DefaultMaterial
  end

  -- Check for built-in Roblox material first
  if typeof(material) == "EnumItem" and MAFSConfig.MaterialSounds[material] then
    return MAFSConfig.MaterialSounds[material]
  end

  -- Check for custom material by name
  if type(material) == "string" and MAFSConfig.CustomMaterials[material] then
    return MAFSConfig.CustomMaterials[material]
  end

  -- Fallback to default material
  return MAFSConfig.DefaultMaterial
end

--[[
    Selects a random sound ID from the material's sound array

    PARAMETERS:
    materialData: MaterialData - Material configuration containing SoundIds

    RETURNS:
    string - Random sound ID from the array

    USAGE:
    Provides audio variation by randomly selecting from available sounds
]]
function MAFSConfig.GetRandomSoundId(materialData: MaterialData): string
  if not materialData or not materialData.SoundIds or #materialData.SoundIds == 0 then
    warn("MAFS: Invalid material data provided to GetRandomSoundId")
    return MAFSConfig.DefaultMaterial.SoundIds[1]
  end

  local soundIds = materialData.SoundIds
  return soundIds[math.random(1, #soundIds)]
end

--[[
    Generates a random playback speed within the material's defined range

    PARAMETERS:
    materialData: MaterialData - Material configuration with PlaybackSpeedRange

    RETURNS:
    number - Random speed between min and max range values

    USAGE:
    Adds natural variation to footstep audio timing
]]
function MAFSConfig.GetRandomPlaybackSpeed(materialData: MaterialData): number
	local range = materialData.PlaybackSpeedRange
	return range[1] + math.random() * (range[2] - range[1])
end

--[[
    Checks if debug mode is currently enabled

    RETURNS:
    boolean - Current debug mode state

    USAGE:
    Used throughout the system for conditional debug logging
]]
function MAFSConfig.IsDebugMode()
  return MAFSConfig.Settings.DebugMode
end

--[[
    Enables or disables debug mode at runtime

    PARAMETERS:
    enabled: boolean - New debug mode state

    USAGE:
    Allows dynamic toggling of debug features during development
]]
function MAFSConfig.SetDebugMode(enabled)
  MAFSConfig.Settings.DebugMode = enabled
end

--[[
    Validates the entire configuration for consistency and correctness

    RETURNS:
    boolean - True if configuration is valid, false otherwise

    VALIDATION CHECKS:
    - All required fields are present
    - Volume levels are within reasonable ranges
    - Sound IDs are properly formatted
    - Rolloff distances are logical
    - No circular references or conflicts

    USAGE:
    Called during system initialization to ensure configuration integrity
]]
function MAFSConfig.ValidateConfiguration()
  -- Validate settings
  local settings = MAFSConfig.Settings
  if not settings then
    warn("MAFS: Settings table is missing")
    return false
  end

  -- Check critical settings
  if
    settings.BroadcastRadius <= 0
    or settings.ServerCooldown <= 0
    or settings.ClientCooldown <= 0
  then
    warn("MAFS: Invalid settings values detected")
    return false
  end

  -- Validate material sounds
  for material, data in pairs(MAFSConfig.MaterialSounds) do
    if not MAFSConfig.ValidateMaterialData(data, tostring(material)) then
      return false
    end
  end

  -- Validate custom materials
  for name, data in pairs(MAFSConfig.CustomMaterials) do
    if not MAFSConfig.ValidateMaterialData(data, name) then
      return false
    end
  end

  -- Validate default material
  if not MAFSConfig.ValidateMaterialData(MAFSConfig.DefaultMaterial, "DefaultMaterial") then
    return false
  end

  return true
end

--[[
    Validates a single MaterialData structure

    PARAMETERS:
    data: MaterialData - Material data to validate
    name: string - Name for error reporting

    RETURNS:
    boolean - True if valid, false otherwise
]]
function MAFSConfig.ValidateMaterialData(data: MaterialData, name: string): boolean
  -- Check sound IDs
  if not data.SoundIds or #data.SoundIds < CONSTANTS.MIN_SOUND_IDS then
    warn(`MAFS: Material '{name}' has insufficient sound IDs`)
    return false
  end

  -- Check volume
  if data.Volume < CONSTANTS.MIN_VOLUME or data.Volume > CONSTANTS.MAX_VOLUME then
    warn(`MAFS: Material '{name}' has invalid volume: {data.Volume}`)
    return false
  end

  -- Check playback speed range
  if
    not data.PlaybackSpeedRange or #data.PlaybackSpeedRange ~= CONSTANTS.PLAYBACK_SPEED_RANGE_SIZE
  then
    warn(`MAFS: Material '{name}' has invalid playback speed range`)
    return false
  end

  -- Check rolloff distances
  if data.RollOffMinDistance >= data.RollOffMaxDistance then
    warn(`MAFS: Material '{name}' has invalid rolloff distances`)
    return false
  end

  -- All checks passed
  return true
end

--[[
    Returns a list of all available material names

    RETURNS:
    { string } - Array of all available material identifiers

    INCLUDES:
    - Built-in Roblox material names
    - Custom material names
    - "Default" for the fallback material

    USAGE:
    Useful for UI generation, debugging, and validation
]]
function MAFSConfig.GetAvailableMaterials(): { string }
  local materials: { string } = {}

  -- Add built-in materials
  for material, _ in pairs(MAFSConfig.MaterialSounds) do
    table.insert(materials, tostring(material))
  end

  -- Add custom materials
  for name, _ in pairs(MAFSConfig.CustomMaterials) do
    table.insert(materials, name)
  end

  -- Add default
  table.insert(materials, "Default")

  return materials
end

-- ============================================================================
-- MODULE EXPORT
-- ============================================================================
return MAFSConfig
