--!strict

--[[
    MAFS_Configuration.luau
    
    Modular Audio FootStep System - Core Configuration Module
    
    Version: 1.1.0
    Author: BleckWolf25
    Contributors: Dynamic Innovative Studio Team
    Copyright: Dynamic Innovative Studio
    
    DESCRIPTION:
    Central configuration module for the MAFS (Modular Audio FootStep) system.
    Provides centralized material sound definitions, system settings, and utility functions.
    
    USAGE:
    local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS_Configuration)
    local grassData = MAFSConfig.GetMaterialData(Enum.Material.Grass)
    local soundId = MAFSConfig.GetRandomSoundId(grassData)
    
    PERFORMANCE:
    - O(1) material lookups via hash tables
    - Optimized random sound selection
    - Cached configuration for session
    
    SECURITY:
    - Runtime validation of all configuration values
    - Rate limiting to prevent abuse
    - Anti-teleport protection via distance validation
]]

-- ============================================================================
-- TYPE ANNOTATIONS & INFER USAGE
-- ============================================================================

--[[
    DATA STRUCTURE DEFINITIONS:

    PlaybackSpeedRange: Array with two numbers (min, max) for speed variation
        Example: { 0.9, 1.1 } means 90% to 110% speed variation

    MaterialData: Table with the following fields:
        SoundIds: Array of Roblox asset ID strings
        Volume: Number (0.0-2.0, typically 0.5-1.2)
        PlaybackSpeedRange: Array with min/max speed variation
        RollOffMinDistance: Number - Distance where volume starts decreasing
        RollOffMaxDistance: Number - Distance where sound becomes inaudible

    MAFSSettings: Table with the following fields:
        DebugMode: Boolean - Enables debug logging
        EnablePerformanceMetrics: Boolean - Tracks system performance
        MaxCachedSounds: Number - Maximum sounds in object pool
        BroadcastRadius: Number - Maximum distance for broadcasting
        ServerCooldown: Number - Minimum time between server validations
        ClientCooldown: Number - Minimum time between client requests
        MovementThreshold: Number - Minimum movement to trigger footstep
        StepInterval: Number - Minimum time between footstep sounds
        MaxDistanceDelta: Number - Anti-teleport protection threshold

    PERFORMANCE CHARACTERISTICS:
    - All material lookups are O(1) hash table operations
    - Sound selection uses optimized random number generation
    - Configuration is loaded once and cached for the session
    - Runtime validation ensures data integrity

    USAGE PATTERNS:
    - Use GetMaterialData() for material resolution with fallback
    - Use GetRandomSoundId() for audio variation
    - Use GetRandomPlaybackSpeed() for natural timing variation
    - Use IsDebugMode() for conditional logging
    - Use ValidateConfiguration() during system initialization
]]

-- ============================================================================
-- DEFINE LUAU MODULE
-- ============================================================================
local MAFSConfig = {}

-- ============================================================================
-- MAFS SYSTEM SETTINGS
-- ============================================================================

-- Core system configuration settings
-- Performance Impact: Higher BroadcastRadius increases network traffic
-- Security: Cooldowns prevent spam, MaxDistanceDelta prevents teleport exploits
-- Audio Quality: Lower StepInterval creates more realistic footsteps
MAFSConfig.Settings = {
  DebugMode = false,                    -- Enable debug logging and diagnostics
  EnablePerformanceMetrics = true,      -- Track system performance metrics
  MaxCachedSounds = 20,                 -- Maximum sounds in object pool
  BroadcastRadius = 50,                 -- Maximum distance for footstep broadcasting (studs)
  ServerCooldown = 0.25,                -- Minimum time between server validations (seconds)
  ClientCooldown = 0.27,                -- Minimum time between client requests
  MovementThreshold = 0.1,              -- Minimum movement distance to trigger footstep (studs)
  StepInterval = 0.3,                   -- Minimum time between footstep sounds (seconds)
  MaxDistanceDelta = 10,                -- Anti-teleport protection threshold
}

-- ============================================================================
-- BUILT-IN MATERIAL SOUND DEFINITIONS
-- ============================================================================

-- Sound configuration for built-in Roblox materials
-- Volume: Reflects material density and hardness (0.5-1.2 range)
-- PlaybackSpeedRange: Adds natural variation (typically ±10-20%)
-- RollOffDistance: Matches real-world sound propagation characteristics
MAFSConfig.MaterialSounds = {
  -- Natural materials
  [Enum.Material.Grass] = {
    SoundIds = {
      "rbxassetid://9118663153",        -- Soft grass step 1
      "rbxassetid://9118663295",        -- Soft grass step 2
      "rbxassetid://9118663485",        -- Soft grass step 3
    },
    Volume = 0.8,                       -- Moderate volume for natural feel
    PlaybackSpeedRange = { 0.9, 1.1 },  -- ±10% speed variation
    RollOffMinDistance = 5,             -- Close proximity for intimate sound
    RollOffMaxDistance = 50,            -- Standard outdoor range
  },

  [Enum.Material.Sand] = {
    SoundIds = {
      "rbxassetid://9118663775",        -- Sand crunch 1
      "rbxassetid://9118664041",        -- Sand crunch 2
      "rbxassetid://9118664270",        -- Sand crunch 3
    },
    Volume = 0.7,                       -- Softer than grass due to absorption
    PlaybackSpeedRange = { 0.9, 1.1 },  -- Natural variation
    RollOffMinDistance = 4,             -- Closer due to muffled nature
    RollOffMaxDistance = 45,            -- Slightly reduced range
  },

  -- Hard surfaces
  [Enum.Material.Concrete] = {
    SoundIds = {
      "rbxassetid://9118662067",        -- Hard concrete step 1
      "rbxassetid://9118662323",        -- Hard concrete step 2
      "rbxassetid://9118662536",        -- Hard concrete step 3
    },
    Volume = 1.0,                       -- Standard reference volume
    PlaybackSpeedRange = { 0.95, 1.05 }, -- Tighter range for consistent feel
    RollOffMinDistance = 6,             -- Slightly further due to hardness
    RollOffMaxDistance = 55,            -- Good propagation on hard surface
  },

  [Enum.Material.Wood] = {
    SoundIds = {
      "rbxassetid://9118664500",        -- Wood creak 1
      "rbxassetid://9118664650",        -- Wood creak 2
      "rbxassetid://9118664800",        -- Wood creak 3
    },
    Volume = 0.9,                       -- Slightly softer than concrete
    PlaybackSpeedRange = { 0.9, 1.1 },  -- Natural wood variation
    RollOffMinDistance = 5,             -- Moderate proximity
    RollOffMaxDistance = 50,            -- Standard indoor/outdoor range
  },

  -- Metallic materials
  [Enum.Material.Metal] = {
    SoundIds = {
      "rbxassetid://9118665000",        -- Metal clang 1
      "rbxassetid://9118665150",        -- Metal clang 2
      "rbxassetid://9118665300",        -- Metal clang 3
    },
    Volume = 1.1,                       -- Louder due to resonance
    PlaybackSpeedRange = { 0.95, 1.05 }, -- Tighter for metallic consistency
    RollOffMinDistance = 7,             -- Further due to sharp sound
    RollOffMaxDistance = 60,            -- Excellent propagation
  },

  [Enum.Material.Rock] = {
    SoundIds = {
      "rbxassetid://9118665500",        -- Rock scrape 1
      "rbxassetid://9118665650",        -- Rock scrape 2
      "rbxassetid://9118665800",        -- Rock scrape 3
    },
    Volume = 1.0,                       -- Standard hard surface volume
    PlaybackSpeedRange = { 0.9, 1.1 },  -- Natural stone variation
    RollOffMinDistance = 6,             -- Similar to concrete
    RollOffMaxDistance = 55,            -- Good outdoor propagation
  },

  -- Special materials
  [Enum.Material.Water] = {
    SoundIds = {
      "rbxassetid://9118666000",        -- Water splash 1
      "rbxassetid://9118666150",        -- Water splash 2
      "rbxassetid://9118666300",        -- Water splash 3
    },
    Volume = 0.6,                       -- Quieter due to absorption
    PlaybackSpeedRange = { 0.8, 1.2 },  -- Wider range for fluid dynamics
    RollOffMinDistance = 4,             -- Close due to muffled nature
    RollOffMaxDistance = 40,            -- Reduced range in water
  },
}

-- ============================================================================
-- CUSTOM MATERIAL SOUND DEFINITIONS
-- ============================================================================

-- Custom materials accessed via FootstepMaterial attribute on parts
-- Usage: part:SetAttribute("FootstepMaterial", "Snow")
-- Naming: Use PascalCase for consistency (e.g., "MetalGrate", not "metal_grate")
-- Implementation: Custom materials override built-in material detection
MAFSConfig.CustomMaterials = {
  -- Weather and environmental materials
  ["Snow"] = {
    SoundIds = {
      "rbxassetid://9118666500",        -- Soft snow crunch 1
      "rbxassetid://9118666650",        -- Soft snow crunch 2
      "rbxassetid://9118666800",        -- Soft snow crunch 3
    },
    Volume = 0.5,                       -- Very quiet due to snow absorption
    PlaybackSpeedRange = { 0.8, 1.0 },  -- Slower range for muffled effect
    RollOffMinDistance = 3,             -- Very close due to absorption
    RollOffMaxDistance = 35,            -- Reduced range in snow
  },

  ["Gravel"] = {
    SoundIds = {
      "rbxassetid://9118667000",        -- Gravel crunch 1
      "rbxassetid://9118667150",        -- Gravel crunch 2
      "rbxassetid://9118667300",        -- Gravel crunch 3
    },
    Volume = 0.9,                       -- Moderate volume with texture
    PlaybackSpeedRange = { 0.9, 1.1 },  -- Natural variation for loose material
    RollOffMinDistance = 5,             -- Standard proximity
    RollOffMaxDistance = 50,            -- Good outdoor propagation
  },

  ["Mud"] = {
    SoundIds = {
      "rbxassetid://9118667500",        -- Wet mud squelch 1
      "rbxassetid://9118667650",        -- Wet mud squelch 2
      "rbxassetid://9118667800",        -- Wet mud squelch 3
    },
    Volume = 0.7,                       -- Muffled by moisture
    PlaybackSpeedRange = { 0.8, 1.0 },  -- Slower for viscous effect
    RollOffMinDistance = 4,             -- Close due to absorption
    RollOffMaxDistance = 40,            -- Reduced range due to dampening
  },

  -- Industrial and structural materials
  ["MetalGrate"] = {
    SoundIds = {
      "rbxassetid://9118668000",        -- Metal grate clang 1
      "rbxassetid://9118668150",        -- Metal grate clang 2
      "rbxassetid://9118668300",        -- Metal grate clang 3
    },
    Volume = 1.2,                       -- Louder due to resonance and echo
    PlaybackSpeedRange = { 0.95, 1.05 }, -- Tight range for metallic consistency
    RollOffMinDistance = 8,             -- Further due to sharp, penetrating sound
    RollOffMaxDistance = 65,            -- Excellent propagation through structure
  },
}

-- ============================================================================
-- DEFAULT MATERIAL CONFIGURATION
-- ============================================================================

-- Fallback material used when no specific material is found
-- Uses concrete sounds as a neutral, common baseline
-- Conservative settings to avoid audio issues
MAFSConfig.DefaultMaterial = {
  SoundIds = {
    "rbxassetid://9118662067",          -- Generic concrete step 1
    "rbxassetid://9118662323",          -- Generic concrete step 2
  },
  Volume = 0.8,                         -- Safe moderate volume
  PlaybackSpeedRange = { 0.9, 1.1 },    -- Standard variation
  RollOffMinDistance = 5,               -- Standard proximity
  RollOffMaxDistance = 50,              -- Standard range
}

-- ============================================================================
-- CORE UTILITY FUNCTIONS
-- ============================================================================

-- Retrieves material data with hierarchical fallback system
-- Resolution Order: 1. Built-in Roblox material, 2. Custom material, 3. Default
-- Performance: O(1) hash table lookups, optimized for frequent calls
function MAFSConfig.GetMaterialData(material)
  -- Check for built-in Roblox material first
  if typeof(material) == "EnumItem" and MAFSConfig.MaterialSounds[material] then
    return MAFSConfig.MaterialSounds[material]
  end

  -- Check for custom material by name
  if type(material) == "string" and MAFSConfig.CustomMaterials[material] then
    return MAFSConfig.CustomMaterials[material]
  end

  -- Fallback to default material
  return MAFSConfig.DefaultMaterial
end

-- Selects a random sound ID from the material's sound array
-- Provides audio variation by randomly selecting from available sounds
function MAFSConfig.GetRandomSoundId(materialData)
  local soundIds = materialData.SoundIds
  return soundIds[math.random(1, #soundIds)]
end

-- Generates a random playback speed within the material's defined range
-- Adds natural variation to footstep audio timing
function MAFSConfig.GetRandomPlaybackSpeed(materialData)
  local range = materialData.PlaybackSpeedRange
  return range[1] + math.random() * (range[2] - range[1])
end

-- Checks if debug mode is currently enabled
-- Used throughout the system for conditional debug logging
function MAFSConfig.IsDebugMode()
  return MAFSConfig.Settings.DebugMode
end

-- Enables or disables debug mode at runtime
-- Allows dynamic toggling of debug features during development
function MAFSConfig.SetDebugMode(enabled)
  MAFSConfig.Settings.DebugMode = enabled
end

-- Validates the entire configuration for consistency and correctness
-- Called during system initialization to ensure configuration integrity
-- Checks: Required fields, volume ranges, sound IDs, rolloff distances
function MAFSConfig.ValidateConfiguration()
  -- Validate settings
  local settings = MAFSConfig.Settings
  if not settings then
    warn("MAFS: Settings table is missing")
    return false
  end

  -- Check critical settings
  if settings.BroadcastRadius <= 0 or settings.ServerCooldown <= 0 or settings.ClientCooldown <= 0 then
    warn("MAFS: Invalid settings values detected")
    return false
  end

  -- Validate material sounds
  for material, data in pairs(MAFSConfig.MaterialSounds) do
    if not MAFSConfig.ValidateMaterialData(data, tostring(material)) then
      return false
    end
  end

  -- Validate custom materials
  for name, data in pairs(MAFSConfig.CustomMaterials) do
    if not MAFSConfig.ValidateMaterialData(data, name) then
      return false
    end
  end

  -- Validate default material
  if not MAFSConfig.ValidateMaterialData(MAFSConfig.DefaultMaterial, "DefaultMaterial") then
    return false
  end

  return true
end

-- Validates a single MaterialData structure
-- Returns true if valid, false otherwise with warning messages
function MAFSConfig.ValidateMaterialData(data, name)
  if not data.SoundIds or #data.SoundIds == 0 then
    warn(string.format("MAFS: Material '%s' has no sound IDs", name))
    return false
  end

  if data.Volume < 0 or data.Volume > 2 then
    warn(string.format("MAFS: Material '%s' has invalid volume: %f", name, data.Volume))
    return false
  end

  if not data.PlaybackSpeedRange or #data.PlaybackSpeedRange ~= 2 then
    warn(string.format("MAFS: Material '%s' has invalid playback speed range", name))
    return false
  end

  if data.RollOffMinDistance >= data.RollOffMaxDistance then
    warn(string.format("MAFS: Material '%s' has invalid rolloff distances", name))
    return false
  end

  return true
end

-- Returns a list of all available material names
-- Includes built-in Roblox materials, custom materials, and "Default"
-- Useful for UI generation, debugging, and validation
function MAFSConfig.GetAvailableMaterials()
  local materials = {}

  -- Add built-in materials
  for material, _ in pairs(MAFSConfig.MaterialSounds) do
    table.insert(materials, tostring(material))
  end

  -- Add custom materials
  for name, _ in pairs(MAFSConfig.CustomMaterials) do
    table.insert(materials, name)
  end

  -- Add default
  table.insert(materials, "Default")

  return materials
end

-- ============================================================================
-- EXPORT LUAU MODULE
-- ============================================================================
return MAFSConfig
