--!strict

--[[
    Ban Command Module

    Allows moderators to ban players from the game.
    Usage: !ban <player> <reason> <duration>
]]

local Players = game:GetService("Players")
local PermissionService = require(script.Parent.Parent.Parent.Core.PermissionService)

local BanCommand = {}

-- Command metadata
BanCommand.Name = "ban"
BanCommand.Description = "Bans a player from the game for a specified duration"
BanCommand.Usage = "!ban <player> <reason> <duration>"
BanCommand.Aliases = { "tempban", "banuser" }
BanCommand.Category = "Moderation" -- Match one of the categories in CommandDispatcher

-- Parse duration string to seconds
-- Examples: "1d" = 1 day, "2h" = 2 hours, "30m" = 30 minutes
local function parseDuration(durationStr)
  if not durationStr then
    return nil
  end

  local value = tonumber(durationStr:match("^(%d+)"))
  local unit = durationStr:match("^%d+(.-)$")

  if not value or not unit then
    return nil
  end

  local multipliers = {
    ["s"] = 1, -- seconds
    ["m"] = 60, -- minutes
    ["h"] = 60 * 60, -- hours
    ["d"] = 60 * 60 * 24, -- days
    ["w"] = 60 * 60 * 24 * 7, -- weeks
  }

  local multiplier = multipliers[unit:lower()]
  if not multiplier then
    return nil
  end

  return value * multiplier
end

-- Get player by name or display name
local function findPlayer(playerName)
  local playerName = playerName:lower()

  -- First try exact username match
  for _, player in ipairs(Players:GetPlayers()) do
    if player.Name:lower() == playerName then
      return player
    end
  end

  -- Then try display name match
  for _, player in ipairs(Players:GetPlayers()) do
    if player.DisplayName:lower() == playerName then
      return player
    end
  end

  -- Try partial name matches
  for _, player in ipairs(Players:GetPlayers()) do
    if player.Name:lower():find(playerName, 1, true) then
      return player
    end

    if player.DisplayName:lower():find(playerName, 1, true) then
      return player
    end
  end

  return nil
end

-- Execute command logic
function BanCommand.Execute(player, args)
  -- Check if player has permission to use this command
  if not PermissionService.canUseCommand(player, "ban") then
    return {
      success = false,
      message = "You don't have permission to use this command",
    }
  end

  -- Validate arguments
  if #args < 3 then
    return {
      success = false,
      message = "Invalid usage. Usage: " .. BanCommand.Usage,
    }
  end

  local targetName = args[1]
  local reason = args[2]
  local durationStr = args[3]

  -- Find target player
  local targetPlayer = findPlayer(targetName)
  if not targetPlayer then
    return {
      success = false,
      message = "Player '" .. targetName .. "' not found",
    }
  end

  -- Parse duration
  local durationSeconds = parseDuration(durationStr)
  if not durationSeconds then
    return {
      success = false,
      message = "Invalid duration format. Examples: 1d, 2h, 30m",
    }
  end

  -- Log the ban action
  print(
    "MCS: Player "
      .. player.Name
      .. " banned "
      .. targetPlayer.Name
      .. " for "
      .. reason
      .. " (Duration: "
      .. durationStr
      .. ")"
  )

  -- Store ban in your persistence system (DataStore)
  -- This is a placeholder - implement your own ban storage system
  local banData = {
    moderator = player.UserId,
    reason = reason,
    endTime = os.time() + durationSeconds,
    timestamp = os.time(),
  }

  print(
    targetPlayer,
    " had been banned by: ",
    player.Name,
    "\nReason: " .. reason .. "\nDuration: " .. durationStr
  )
  -- local DataStoreService = game:GetService("DataStoreService")
  -- local BanStore = DataStoreService:GetDataStore("PlayerBans")
  -- local success, err = pcall(function()
  --     BanStore:SetAsync("ban_" .. targetPlayer.UserId, banData)
  -- end)

  -- For now, just kick the player
  targetPlayer:Kick(
    "\nYou have been banned by "
      .. player.Name
      .. "\nReason: "
      .. reason
      .. "\nDuration: "
      .. durationStr
  )

  return {
    success = true,
    message = "Successfully banned "
      .. targetPlayer.Name
      .. " for "
      .. reason
      .. " (Duration: "
      .. durationStr
      .. ")",
  }
end

return BanCommand
