--[[
    MCS: Roblox Modular Command System
    Server Entry Point

    This script initializes the server-side components of the MCS.
    It sets up the remote events, loads command modules, and connects handlers.
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")

-- Create MCS folder structure if it doesn't exist
local function ensurePathExists(parent, pathParts)
  local current = parent
  for _, part in ipairs(pathParts) do
    local found = current:FindFirstChild(part)
    if not found then
      found = Instance.new("Folder")
      found.Name = part
      found.Parent = current
    end
    current = found
  end
  return current
end

-- Set up folder structure
local MCSReplicated = ensurePathExists(ReplicatedStorage, { "MCS", "Remotes" })

-- Create and configure remote events
local commandRemote = Instance.new("RemoteEvent")
commandRemote.Name = "CommandRemote"
commandRemote.Parent = MCSReplicated

local autocompleteRemote = Instance.new("RemoteFunction")
autocompleteRemote.Name = "AutocompleteRemote"
autocompleteRemote.Parent = MCSReplicated

-- Load command dispatcher
local CommandDispatcher = require(script.Parent.CommandDispatcher)
local Middleware = require(script.Parent.Middleware.MCS_MiddlewareInitiliazation)
local PermissionService = require(script.Parent.PermissionService)

-- Prints Debug message
print("MCS Server initializing...")

-- Initialize services
PermissionService.init()
Middleware.init() -- Initialize middleware if it has an init function
CommandDispatcher.init() -- Initialize the command dispatcher to load commands

-- Connect remote events
commandRemote.OnServerEvent:Connect(function(player, commandText)
  -- Process command
  local success, result = CommandDispatcher.processCommand(player, commandText)

  -- Send feedback to client (could also use a separate feedback remote)
  commandRemote:FireClient(player, success, result)
end)

-- Configure autocomplete handler
autocompleteRemote.OnServerInvoke = function(player, partialCommand)
  return CommandDispatcher.getAutocompleteSuggestions(player, partialCommand)
end

print("MCS Server initialized successfully")
