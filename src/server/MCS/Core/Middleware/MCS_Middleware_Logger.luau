--!strict

--[[
    Logger Middleware

    Records command usage for auditing and debugging:
    - Logs to console for immediate visibility
    - Logs to DataStore for persistence
    - Supports filtering sensitive information
]]

local Logger = {}

local DataStoreService = game:GetService("DataStoreService")
local HttpService = game:GetService("HttpService")

-- Configuration
local CONFIG = {
	-- Enable console logging
	CONSOLE_LOGGING = true,

	-- Enable DataStore logging
	DATASTORE_LOGGING = true,

	-- Enable webhook logging
	WEBHOOK_LOGGING = false,

	-- Discord webhook URL
	WEBHOOK_URL = "",

	-- DataStore name for command logs
	DATASTORE_NAME = "MCS_CommandLogs",

	-- Maximum log entries to keep in DataStore (per key)
	MAX_LOG_ENTRIES = 100,
}

-- List of commands that shouldn't log their arguments (for privacy/security)
local SENSITIVE_COMMANDS = {
	"login",
	"password",
}

-- DataStore for persistent logging (if enabled)
local commandLogStore

-- Initialize logger
function Logger.init()
	if CONFIG.DATASTORE_LOGGING then
		commandLogStore = DataStoreService:GetDataStore(CONFIG.DATASTORE_NAME)
	end
end

-- Check if a command is sensitive (shouldn't log full details)
local function isCommandSensitive(commandName)
	for _, name in ipairs(SENSITIVE_COMMANDS) do
		if name:lower() == commandName:lower() then
			return true
		end
	end
	return false
end

-- Format log entry
local function formatLogEntry(player, commandName, args)
	local logEntry = {
		timestamp = os.time(),
		player = {
			userId = player.UserId,
			name = player.Name,
		},
		command = commandName,
	}

	-- Only include arguments for non-sensitive commands
	if not isCommandSensitive(commandName) then
		logEntry.arguments = args
	end

	return logEntry
end

-- Log command to console
local function logToConsole(player, commandName, args)
	local argsStr = ""
	if not isCommandSensitive(commandName) and args then
		argsStr = table.concat(args, " ")
	end

	print(
		string.format(
			"[MCS Log] Player: %s (%d) | Command: %s | Args: %s",
			player.Name,
			player.UserId,
			commandName,
			argsStr
		)
	)
end

-- Log command to DataStore
local function logToDataStore(player, commandName, args)
	if not commandLogStore then
		return
	end

	local logEntry = formatLogEntry(player, commandName, args)
	local logKey = "log_" .. os.date("%Y%m%d") -- Group logs by date

	-- Async to avoid blocking
	task.spawn(function()
		local success, result = pcall(function()
			-- Read existing logs for today
			local currentLogs = commandLogStore:GetAsync(logKey) or {}

			-- Add new log
			table.insert(currentLogs, logEntry)

			-- Keep only the most recent logs
			if #currentLogs > CONFIG.MAX_LOG_ENTRIES then
				-- Remove oldest logs
				local newLogs = {}
				for i = #currentLogs - CONFIG.MAX_LOG_ENTRIES + 1, #currentLogs do
					table.insert(newLogs, currentLogs[i])
				end
				currentLogs = newLogs
			end

			-- Save updated logs
			commandLogStore:SetAsync(logKey, currentLogs)
		end)

		if not success then
			warn("MCS Logger: Failed to save logs to DataStore:", result)
		end
	end)
end

-- Log command to webhook (discord)
local function logToWebhook(player, commandName, args)
	if not CONFIG.WEBHOOK_URL or #CONFIG.WEBHOOK_URL == 0 then
		return
	end

	local logEntry = formatLogEntry(player, commandName, args)

	-- Format for Discord
	local webhookData = {
		content = string.format(
			"**Command Used**\nPlayer: %s (%d)\nCommand: %s",
			player.Name,
			player.UserId,
			commandName
		),
	}

	-- Add args if not sensitive
	if not isCommandSensitive(commandName) and args and #args > 0 then
		webhookData.content = webhookData.content .. "\nArgs: " .. table.concat(args, " ")
	end

	-- Send asynchronously
	task.spawn(function()
		local success, result = pcall(function()
			HttpService:PostAsync(
				CONFIG.WEBHOOK_URL,
				HttpService:JSONEncode(webhookData),
				Enum.HttpContentType.ApplicationJson
			)
		end)

		if not success then
			warn("MCS Logger: Failed to send webhook:", result)
		end
	end)
end

-- Process middleware (returns true to allow command to continue)
function Logger.process(player, commandName, args)
	-- Log to console if enabled
	if CONFIG.CONSOLE_LOGGING then
		print(player, commandName, args)
		logToConsole(player, commandName, args)
	end

	-- Log to DataStore if enabled
	if CONFIG.DATASTORE_LOGGING then
		print(player, commandName, args)
		logToDataStore(player, commandName, args)
	end

	-- Log to webhook if enabled
	if CONFIG.WEBHOOK_LOGGING then
		logToWebhook(player, commandName, args)
	end

	-- Always allow command to continue
	return true
end

return Logger
