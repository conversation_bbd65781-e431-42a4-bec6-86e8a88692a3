--!strict

--[[
    - file: MAFS_Server_Initilization.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Server-side initialization script for the Modular Audio FootStep System (MAFS)
      - Loads the MAFS Server Manager module
      - Demonstrates the usage of the public API
]]

-- ============================================================================
-- REFERENCES
-- ============================================================================
local ServerManager = script.Parent:FindFirstChild("MAFS_Server_Manager")

-- Check if the server module exists
if not ServerManager then
  warn("MAFS: Manager module not found in expected location")
  return
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

-- Initialize the server-side MAFS system
local ServerManager = require(script.Parent:WaitForChild("MAFS_Server_Manager"))
local success = ServerManager.Initialize()

-- Handle initialization result
if success then
  print("MAFS: Server-side MAFS system initialized successfully")
else
  warn("MAFS: Failed to initialize server-side MAFS system")
end

--[[
    Public API Examples

    The footstep system provides the following public APIs that can be used
    by other server scripts to interact with the system:

    -- Example: Broadcast a footstep event (usually handled internally by the system)
    ServerManager.BroadcastFootstep(player, audioData)
]]
