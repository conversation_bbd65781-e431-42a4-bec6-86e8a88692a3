--!strict

--[[
 - file: MAFS_SERVER_MANAGER.LUAU

 - version: 2.0.0
 - author: <PERSON><PERSON><PERSON><PERSON>olf25
 - contributors:

 - copyright: Dynamic Innovative Studio

 - description:
   - Server-side module for the Modular Audio FootStep System (MAFS).
   - Handles footstep requests from clients.
   - Validates requests for security and performance.
   - Broadcasts footstep sounds to nearby players.
   - Should be required by the server-side initialization script.
]]

local ServerManager = {}

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Import MAFS modules
local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS_Configuration)
local MAFSRemotes = require(ReplicatedStorage.MAFS.MAFS_Remotes)
local MaterialConfig = require(ReplicatedStorage.MAFS.Shared.MAFS_Material_Configuration)
local PerformanceMonitor = require(ReplicatedStorage.MAFS.Shared.MAFS_PerformanceMonitor)

-- Initialize remotes
local FootstepEvent = MAFSRemotes.GetFootstepEvent()

-- Constants from configuration
local SERVER_COOLDOWN = MAFSConfig.Settings.ServerCooldown
local MAX_DISTANCE_DELTA = MAFSConfig.Settings.MaxDistanceDelta
local BROADCAST_RADIUS = MAFSConfig.Settings.BroadcastRadius

-- Player state tracking
local playerStates = {}

--[[
    Gets material data with support for custom materials via attributes

    @param humanoid (Humanoid) - The humanoid to check for floor material
    @param position (Vector3) - The position to check for custom materials
    @return (table) - Material data containing sound configuration
]]
function ServerManager.GetMaterialData(humanoid, position)
  return MaterialConfig.ResolveMaterial(position, humanoid)
end

--[[
    Gets a random sound ID for the specified material data

    @param materialData (table) - The material data to get a sound for
    @return (string) - A random sound ID for the material
]]
function ServerManager.GetRandomSoundForMaterial(materialData)
  return MAFSConfig.GetRandomSoundId(materialData)
end

--[[
    Gets a random playback speed within the range for the material data

    @param materialData (table) - The material data to get a playback speed for
    @return (number) - A random playback speed within the material's range
]]
function ServerManager.GetRandomPlaybackSpeed(materialData)
  return MAFSConfig.GetRandomPlaybackSpeed(materialData)
end

--[[
    Gets the volume for the specified material data

    @param materialData (table) - The material data to get a volume for
    @return (number) - The volume for the material
]]
function ServerManager.GetVolumeForMaterial(materialData)
  return materialData.Volume
end

--[[
    Validates a footstep request from a client

    @param player (Player) - The player who sent the request
    @param position (Vector3) - The position where the footstep occurred
    @return (boolean) - Whether the request is valid
]]
function ServerManager.ValidateFootstep(player, position)
  -- Initialize player state if it doesn't exist
  if not playerStates[player.UserId] then
    playerStates[player.UserId] = {
      lastFootstepTime = 0,
      lastPosition = position,
    }
    return true
  end

  local state = playerStates[player.UserId]
  local currentTime = tick()

  -- Check cooldown
  if currentTime - state.lastFootstepTime < SERVER_COOLDOWN then
    PerformanceMonitor.TrackValidationFailure()
    return false
  end

  -- Check for teleporting (anti-cheat)
  local distanceDelta = (position - state.lastPosition).Magnitude
  if distanceDelta > MAX_DISTANCE_DELTA then
    PerformanceMonitor.TrackValidationFailure()
    return false
  end

  -- Update player state
  state.lastFootstepTime = currentTime
  state.lastPosition = position

  return true
end

--[[
    Handles a footstep request from a client

    @param player (Player) - The player who sent the request
    @param position (Vector3) - The position where the footstep occurred
]]
function ServerManager.HandleFootstepRequest(player, position)
  -- Track the request
  PerformanceMonitor.TrackFootstepRequest()

  -- Validate the request
  if not ServerManager.ValidateFootstep(player, position) then
    return
  end

  -- Get the character and humanoid
  local character = player.Character
  if not character then
    return
  end

  local humanoid = character:FindFirstChild("Humanoid")
  if not humanoid then
    return
  end

  -- Get material data (supports custom materials via attributes)
  local materialData = ServerManager.GetMaterialData(humanoid, position)
  if not materialData then
    PerformanceMonitor.TrackMaterialResolutionError()
    return
  end

  -- Create audio data for the footstep
  local audioData = {
    SoundId = ServerManager.GetRandomSoundForMaterial(materialData),
    Volume = ServerManager.GetVolumeForMaterial(materialData),
    PlaybackSpeed = ServerManager.GetRandomPlaybackSpeed(materialData),
    Position = position,
    RollOffMinDistance = materialData.RollOffMinDistance or 5,
    RollOffMaxDistance = materialData.RollOffMaxDistance or 50,
  }

  -- Broadcast to nearby players
  ServerManager.BroadcastFootstep(player, audioData)

  -- Debug logging
  if MAFSConfig.IsDebugMode() then
    print(string.format("MAFS: Footstep for %s at %s", player.Name, tostring(position)))
  end
end

--[[
    Broadcasts a footstep sound to nearby players

    @param sourcePlayer (Player) - The player who made the footstep
    @param audioData (table) - The audio data for the footstep
]]
function ServerManager.BroadcastFootstep(sourcePlayer, audioData)
  local sourcePosition = audioData.Position
  local playersNotified = 0

  -- Find nearby players
  for _, player in ipairs(Players:GetPlayers()) do
    -- Skip the source player (they'll handle their own footsteps)
    if player ~= sourcePlayer then
      -- Check if player is within broadcast radius
      local character = player.Character
      if character then
        local rootPart = character:FindFirstChild("HumanoidRootPart")
        if rootPart then
          local distance = (rootPart.Position - sourcePosition).Magnitude
          if distance <= BROADCAST_RADIUS then
            -- Send footstep audio data to this player
            FootstepEvent:FireClient(player, audioData)
            playersNotified = playersNotified + 1
          end
        end
      end
    end
  end

  -- Always send to the source player
  FootstepEvent:FireClient(sourcePlayer, audioData)
  playersNotified = playersNotified + 1

  -- Track the broadcast
  PerformanceMonitor.TrackFootstepBroadcast(playersNotified)
end

--[[
    Cleans up resources when a player leaves

    @param player (Player) - The player who left
]]
function ServerManager.CleanupPlayer(player)
  playerStates[player.UserId] = nil
end

--[[
    Initializes the server-side footstep system
    - Sets up event connections
    - Prepares player tracking
]]
function ServerManager.Initialize()
  -- Initialize performance monitoring
  PerformanceMonitor.Initialize()

  -- Initialize remotes first
  if not MAFSRemotes.Initialize() then
    warn("MAFS: Failed to initialize remotes, system cannot start")
    return false
  end

  -- Get the footstep event
  FootstepEvent = MAFSRemotes.GetFootstepEvent()
  if not FootstepEvent then
    warn("MAFS: Failed to get footstep event")
    return false
  end

  -- Connect to the footstep event
  FootstepEvent.OnServerEvent:Connect(function(player, position)
    ServerManager.HandleFootstepRequest(player, position)
  end)

  -- Clean up when players leave
  Players.PlayerRemoving:Connect(function(player)
    ServerManager.CleanupPlayer(player)
  end)

  print("MAFS: ServerManager initialized successfully")
  return true
end

--[[
    SECURITY NOTE:

    This implementation uses a combined approach for security:

    1. Client detects movement locally (for performance)
    2. Server validates all requests (for security)
    3. Server performs material detection (for consistency)
    4. Server broadcasts to nearby players only (for optimization)

    If more security is required because of exploited, you can implement the following with MAFS:
    - More strict position validation
    - Server-side movement simulation
    - Rate limiting at the player level
    - Anti-exploitation monitoring
]]

return ServerManager
