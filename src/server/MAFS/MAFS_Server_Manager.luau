--!strict

--[[
 - file: MAFS_SERVER_MANAGER.LUAU

 - version: 2.1.0
 - author: BleckWolf25
 - contributors:

 - copyright: Dynamic Innovative Studio

 - description:
   - Server-side module for the Modular Audio FootStep System (MAFS).
   - Handles real-time footstep validation, material detection, and sound broadcasting.
   - Implements anti-exploit protection with position validation and rate limiting.
   - Manages player state tracking and proximity-based sound distribution.
   - Integrates with MAFS configuration system for material-based sound mapping.
   - Should be required by the server-side initialization script.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS_Configuration)
local MAFSRemotes = require(ReplicatedStorage:WaitForChild("MAFS"):WaitForChild("MAFS_Remotes"))
local MaterialConfig = require(
  ReplicatedStorage:WaitForChild("MAFS")
    :WaitForChild("Shared")
    :WaitForChild("MAFS_Material_Configuration")
)
local PerformanceMonitor = require(
  ReplicatedStorage:WaitForChild("MAFS")
    :WaitForChild("Shared")
    :WaitForChild("MAFS_PerformanceMonitor")
)

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local SERVER_COOLDOWN = MAFSConfig.Settings.ServerCooldown
local MAX_DISTANCE_DELTA = MAFSConfig.Settings.MaxDistanceDelta
local BROADCAST_RADIUS = MAFSConfig.Settings.BroadcastRadius

-- ============================================================================
-- MODULE REFERENCES
-- ============================================================================
local FootstepEvent = MAFSRemotes.GetFootstepEvent()

-- ============================================================================
-- TYPES
-- ============================================================================

-- Audio configuration data structure for footstep sounds
export type AudioData = {
  SoundId: string, -- Roblox asset ID for the sound
  Volume: number, -- Sound volume (0.0-2.0)
  PlaybackSpeed: number, -- Playback speed multiplier
  Position: Vector3, -- 3D world position for spatial audio
  RollOffMinDistance: number?, -- Distance where volume starts decreasing
  RollOffMaxDistance: number?, -- Distance where sound becomes inaudible
}

-- Player state tracking for validation and anti-exploit protection
type PlayerState = {
  lastFootstepTime: number, -- Timestamp of last validated footstep
  lastPosition: Vector3, -- Last known valid position
}

-- Player state storage indexed by UserId
type PlayerStates = { [number]: PlayerState }

-- Material data structure from configuration system
type MaterialData = {
  SoundIds: { string }, -- Array of sound asset IDs
  Volume: number, -- Base volume for this material
  PlaybackSpeedRange: { number }, -- Min/max playback speed range
  RollOffMinDistance: number?, -- Optional minimum rolloff distance
  RollOffMaxDistance: number?, -- Optional maximum rolloff distance
}

-- Module interface for external usage
export type ServerManagerModule = {
  GetMaterialData: (humanoid: Humanoid, position: Vector3) -> MaterialData?,
  GetRandomSoundForMaterial: (materialData: MaterialData) -> string,
  GetRandomPlaybackSpeed: (materialData: MaterialData) -> number,
  GetVolumeForMaterial: (materialData: MaterialData) -> number,
  ValidateFootstep: (player: Player, position: Vector3) -> boolean,
  HandleFootstepRequest: (player: Player, position: Vector3) -> (),
  BroadcastFootstep: (sourcePlayer: Player, audioData: AudioData) -> (),
  CleanupPlayer: (player: Player) -> (),
  Initialize: () -> boolean,
}

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local ServerManager = {}

-- ============================================================================
-- MODULE STATE
-- ============================================================================

-- Player state tracking for validation and anti-exploit protection
-- Stores last footstep time and position for each player
local playerStates: PlayerStates = {}

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

--[[
    Gets material data with support for custom materials via attributes

    @param humanoid (Humanoid) - The humanoid to check for floor material
    @param position (Vector3) - The position to check for custom materials
    @return (MaterialData?) - Material data containing sound configuration
]]
function ServerManager.GetMaterialData(humanoid: Humanoid, position: Vector3): MaterialData?
  local result = MaterialConfig.ResolveMaterial(position, humanoid)
  return result :: MaterialData?
end

--[[
    Gets a random sound ID for the specified material data

    @param materialData (MaterialData) - The material data to get a sound for
    @return (string) - A random sound ID for the material
]]
function ServerManager.GetRandomSoundForMaterial(materialData: MaterialData): string
  return MAFSConfig.GetRandomSoundId(materialData)
end

--[[
    Gets a random playback speed within the range for the material data

    @param materialData (MaterialData) - The material data to get a playback speed for
    @return (number) - A random playback speed within the material's range
]]
function ServerManager.GetRandomPlaybackSpeed(materialData: MaterialData): number
  return MAFSConfig.GetRandomPlaybackSpeed(materialData)
end

--[[
    Gets the volume for the specified material data

    @param materialData (MaterialData) - The material data to get a volume for
    @return (number) - The volume for the material
]]
function ServerManager.GetVolumeForMaterial(materialData: MaterialData): number
  return materialData.Volume
end

-- ============================================================================
-- VALIDATION FUNCTIONS
-- ============================================================================

--[[
    Validates a footstep request from a client

    @param player (Player) - The player who sent the request
    @param position (Vector3) - The position where the footstep occurred
    @return (boolean) - Whether the request is valid
]]
function ServerManager.ValidateFootstep(player: Player, position: Vector3): boolean
  -- Initialize player state if it doesn't exist
  if not playerStates[player.UserId] then
    playerStates[player.UserId] = {
      lastFootstepTime = 0,
      lastPosition = position,
    }
    return true
  end

  local state = playerStates[player.UserId]
  local currentTime = tick()

  -- Check cooldown
  if currentTime - state.lastFootstepTime < SERVER_COOLDOWN then
    PerformanceMonitor.TrackValidationFailure()
    return false
  end

  -- Check for teleporting (anti-cheat)
  local distanceDelta = (position - state.lastPosition).Magnitude
  if distanceDelta > MAX_DISTANCE_DELTA then
    PerformanceMonitor.TrackValidationFailure()
    return false
  end

  -- Update player state
  state.lastFootstepTime = currentTime
  state.lastPosition = position

  return true
end

-- ============================================================================
-- REQUEST HANDLING
-- ============================================================================

--[[
    Handles a footstep request from a client

    @param player (Player) - The player who sent the request
    @param position (Vector3) - The position where the footstep occurred
]]
function ServerManager.HandleFootstepRequest(player: Player, position: Vector3): ()
  -- Track the request
  PerformanceMonitor.TrackFootstepRequest()

  -- Validate the request
  if not ServerManager.ValidateFootstep(player, position) then
    return
  end

  -- Get the character and humanoid
  local character = player.Character
  if not character then
    return
  end

  local humanoid = character:FindFirstChild("Humanoid") :: Humanoid?
  if not humanoid then
    return
  end

  -- Get material data (supports custom materials via attributes)
  local materialData = ServerManager.GetMaterialData(humanoid, position)
  if not materialData then
    PerformanceMonitor.TrackMaterialResolutionError()
    return
  end

  -- Create audio data for the footstep
  local audioData: AudioData = {
    SoundId = ServerManager.GetRandomSoundForMaterial(materialData),
    Volume = ServerManager.GetVolumeForMaterial(materialData),
    PlaybackSpeed = ServerManager.GetRandomPlaybackSpeed(materialData),
    Position = position,
    RollOffMinDistance = materialData.RollOffMinDistance or 5,
    RollOffMaxDistance = materialData.RollOffMaxDistance or 50,
  }

  -- Broadcast to nearby players
  ServerManager.BroadcastFootstep(player, audioData)

  -- Debug logging
  if MAFSConfig.IsDebugMode() then
    print(string.format("MAFS: Footstep for %s at %s", player.Name, tostring(position)))
  end
end

-- ============================================================================
-- BROADCASTING FUNCTIONS
-- ============================================================================

--[[
    Broadcasts a footstep sound to nearby players

    @param sourcePlayer (Player) - The player who made the footstep
    @param audioData (AudioData) - The audio data for the footstep
]]
function ServerManager.BroadcastFootstep(sourcePlayer: Player, audioData: AudioData): ()
  local sourcePosition = audioData.Position
  local playersNotified = 0

  -- Find nearby players within broadcast radius
  for _, player in Players:GetPlayers() do
    -- Skip the source player (they'll handle their own footsteps)
    if player ~= sourcePlayer then
      -- Check if player is within broadcast radius
      local character = player.Character
      if character then
        local rootPart = character:FindFirstChild("HumanoidRootPart") :: Part?
        if rootPart then
          local distance = (rootPart.Position - sourcePosition).Magnitude
          if distance <= BROADCAST_RADIUS then
            -- Send footstep audio data to this player
            local currentFootstepEvent = FootstepEvent
            if currentFootstepEvent then
              currentFootstepEvent:FireClient(player, audioData)
              playersNotified = playersNotified + 1
            end
          end
        end
      end
    end
  end

  -- Always send to the source player
  local currentFootstepEvent = FootstepEvent
  if currentFootstepEvent then
    currentFootstepEvent:FireClient(sourcePlayer, audioData)
    playersNotified = playersNotified + 1
  end

  -- Track the broadcast
  PerformanceMonitor.TrackFootstepBroadcast(playersNotified)
end

-- ============================================================================
-- CLEANUP FUNCTIONS
-- ============================================================================

--[[
    Cleans up resources when a player leaves

    @param player (Player) - The player who left
]]
function ServerManager.CleanupPlayer(player: Player): ()
  playerStates[player.UserId] = nil
end

-- ============================================================================
-- INITIALIZATION FUNCTION
-- ============================================================================

--[[
    Initializes the server-side footstep system
    - Sets up event connections
    - Prepares player tracking
    - Configures performance monitoring
]]
function ServerManager.Initialize(): boolean
  -- Initialize performance monitoring
  PerformanceMonitor.Initialize()

  -- Initialize remotes first
  if not MAFSRemotes.Initialize() then
    warn("MAFS: Failed to initialize remotes, system cannot start")
    return false
  end

  -- Get the footstep event
  local footstepEventResult = MAFSRemotes.GetFootstepEvent()
  if not footstepEventResult then
    warn("MAFS: Failed to get footstep event")
    return false
  end
  FootstepEvent = footstepEventResult

  -- Connect to the footstep event
  local currentFootstepEvent = FootstepEvent
  if currentFootstepEvent then
    currentFootstepEvent.OnServerEvent:Connect(function(player: Player, position: Vector3)
      ServerManager.HandleFootstepRequest(player, position)
    end)
  end

  -- Clean up when players leave
  Players.PlayerRemoving:Connect(function(player: Player)
    ServerManager.CleanupPlayer(player)
  end)

  print("MAFS: ServerManager initialized successfully")
  return true
end

-- ============================================================================
-- SECURITY DOCUMENTATION
-- ============================================================================

--[[
    SECURITY ARCHITECTURE:

    This implementation uses a layered security approach:

    1. **Client-Side Detection**: Movement detection happens locally for performance
    2. **Server-Side Validation**: All requests are validated on the server for security
    3. **Anti-Exploit Protection**: Position validation and rate limiting prevent abuse
    4. **Material Consistency**: Server performs material detection for consistency
    5. **Proximity Broadcasting**: Only nearby players receive footstep sounds

    ADDITIONAL SECURITY MEASURES (if needed):
    - Stricter position validation with movement prediction
    - Server-side movement simulation for critical validation
    - Per-player rate limiting with escalating penalties
    - Real-time anti-exploitation monitoring and logging
    - Automatic player flagging for suspicious behavior patterns

    PERFORMANCE CONSIDERATIONS:
    - Player state tracking is memory-efficient with automatic cleanup
    - Broadcast radius limits network traffic to relevant players
    - Material resolution is cached for optimal performance
    - Performance monitoring tracks system health metrics
]]

-- ============================================================================
-- EXPORTS
-- ============================================================================
return ServerManager :: ServerManagerModule
