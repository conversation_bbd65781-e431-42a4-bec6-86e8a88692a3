local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local sprintEvent = ReplicatedStorage:WaitFor<PERSON>hild("SprintEvent")

local maxStamina = 100
local minStamina = 0
local staminaDrain = 10
local staminaRecharge = 10

local walkSpeedNormal = 16
local walkSpeedSprint = 32

-- Your script is asking for SprintEvent --- Fixed

local playerData = {}

Players.PlayerAdded:Connect(function(player)
	player.CharacterAdded:Connect(function(character)
		local humanoid = character:WaitFor<PERSON>hild("Humanoid")

		playerData[player] = {
			Sprinting = false,
			Stamina = maxStamina,
			Humanoid = humanoid,
		}
	end)
end)

local function handleSprint(player, action)
	local data = playerData[player]
	if not data then
		return
	end

	local humanoid = data.Humanoid
	if not humanoid or humanoid.Health <= 0 then
		return
	end

	if action == "Start" then
		if not data.Sprinting and data.Stamina > 0 then
			data.Sprinting = true
			humanoid.WalkSpeed = walkSpeedSprint
			task.spawn(function()
				while data.Sprinting and data.Stamina > minStamina do
					data.Stamina -= staminaDrain
					print(player.Name .. " Stamina: " .. data.Stamina)
					task.wait(0.8)
					if data.Stamina <= minStamina then
						humanoid.WalkSpeed = walkSpeedNormal
						break
					end
				end
			end)
		end
	elseif action == "Stop" then
		data.Sprinting = false
		humanoid.WalkSpeed = walkSpeedNormal
		task.spawn(function()
			task.wait(2.5)
			while not data.Sprinting and data.Stamina < maxStamina do
				data.Stamina += staminaRecharge
				print(player.Name .. " Recharging: " .. data.Stamina)
				task.wait(0.6)
			end
		end)
	end
end

sprintEvent.OnServerEvent:Connect(handleSprint)
