-- soon: !strict

--[[
    TBRDS Tag Service

    Core tag assignment and management service for TBRDS

    ARCHITECTURE ROLE:
    - Orchestrates tag assignment and management
    - Coordinates between role, billboard, and other services
    - <PERSON><PERSON> tag lifecycle and state management
    - Manages tag-related events and notifications

    RESPONSIBILITIES:
    - Assign and update player tags
    - Coordinate with RoleService for role determination
    - Coordinate with BillboardService for visual representation
    - Handle tag change events and notifications
    - Manage tag persistence and caching
    - Provide tag-related API endpoints

    *Dynamic Innovative Studio*
]]

local DataStoreService = game:GetService("DataStoreService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Import modules
local EventSystem = require(ReplicatedStorage.TBRDS.Shared.EventSystem)
local PerformanceMonitor = require(ReplicatedStorage.TBRDS.Shared.PerformanceMonitor)
local TBRDSRemotes = require(ReplicatedStorage.TBRDS.Remotes)
local Types = require(ReplicatedStorage.TBRDS.Shared.Types)
local Utils = require(ReplicatedStorage.TBRDS.Shared.Utils)

-- Import services
local BillboardService = require(script.Parent.BillboardService)
local ConfigurationService = require(script.Parent.ConfigurationService)
local RoleService = require(script.Parent.RoleService)

type PlayerTagData = Types.PlayerTagData
type ValidationResult = Types.ValidationResult
type TagEventData = Types.TagEventData

local TagService = {}

-- Service state
local isInitialized = false
local playerTags: { [Player]: PlayerTagData } = {}
local rateLimiter = nil
local specialTagsStore = nil

-- Remote events
local tagUpdateRemote = nil
local tagRequestRemote = nil

-- Debug logging
local function debugLog(message: string)
  if ConfigurationService.IsDebugMode() then
    print(string.format("[TBRDS:TagService]: %s", message))
  end
end

-- Initialize the tag service
function TagService.Initialize(): boolean
  if isInitialized then
    debugLog("Tag service already initialized")
    return true
  end

  debugLog("Initializing tag service...")

  -- Initialize dependencies
  if not ConfigurationService.Initialize() then
    warn("TBRDS: Failed to initialize ConfigurationService")
    return false
  end

  if not RoleService.Initialize() then
    warn("TBRDS: Failed to initialize RoleService")
    return false
  end

  if not BillboardService.Initialize() then
    warn("TBRDS: Failed to initialize BillboardService")
    return false
  end

  -- Initialize rate limiter
  local rateLimitConfig = ConfigurationService.GetRateLimitConfig()
  rateLimiter = Utils.createRateLimiter(rateLimitConfig.Window, rateLimitConfig.MaxRequests)

  -- Initialize DataStore
  specialTagsStore = DataStoreService:GetDataStore("SpecialTags")

  -- Initialize remote events
  tagUpdateRemote = TBRDSRemotes.GetTagUpdateRemote()
  tagRequestRemote = TBRDSRemotes.GetTagRequestRemote()

  if not tagUpdateRemote or not tagRequestRemote then
    warn("TBRDS: Failed to initialize remote events")
    return false
  end

  -- Set up event handlers
  TagService.SetupEventHandlers()

  -- Set up player management
  TagService.SetupPlayerManagement()

  isInitialized = true
  debugLog("Tag service initialized successfully")
  return true
end

-- Set up event handlers
function TagService.SetupEventHandlers(): ()
  -- Handle tag requests from clients
  tagRequestRemote.OnServerEvent:Connect(function(player)
    TagService.HandleTagRequest(player)
  end)

  -- Subscribe to configuration changes
  ConfigurationService.SubscribeToChanges(function(newConfig)
    debugLog("Configuration changed, updating rate limiter")
    rateLimiter = Utils.createRateLimiter(
      newConfig.Settings.RateLimit.Window,
      newConfig.Settings.RateLimit.MaxRequests
    )
  end)
end

-- Set up player management
function TagService.SetupPlayerManagement(): ()
  -- Handle new players
  Players.PlayerAdded:Connect(function(player)
    TagService.HandlePlayerJoined(player)
  end)

  -- Handle players leaving
  Players.PlayerRemoving:Connect(function(player)
    TagService.HandlePlayerLeaving(player)
  end)

  -- Handle existing players
  for _, player in ipairs(Players:GetPlayers()) do
    TagService.HandlePlayerJoined(player)
  end
end

-- Handle player joining
function TagService.HandlePlayerJoined(player: Player): ()
  debugLog(string.format("Player joined: %s", player.Name))

  -- Assign initial tag
  TagService.AssignTag(player)

  -- Set up character spawning handler
  player.CharacterAdded:Connect(function(character)
    TagService.HandleCharacterSpawned(player, character)
  end)

  -- Set up property change handlers
  player:GetPropertyChangedSignal("Team"):Connect(function()
    TagService.RefreshPlayerTag(player)
  end)

  -- Start group rank monitoring
  TagService.StartGroupRankMonitoring(player)
end

-- Handle character spawning
function TagService.HandleCharacterSpawned(player: Player, character: Model): ()
  debugLog(string.format("Character spawned for: %s", player.Name))

  -- Wait for head to load
  local head = character:WaitForChild("Head", 5)
  if not head then
    warn(string.format("TBRDS: Head not found for %s", player.Name))
    return
  end

  -- Get current tag data
  local tagData = playerTags[player]
  if not tagData then
    TagService.AssignTag(player)
    tagData = playerTags[player]
  end

  if tagData then
    -- Create billboard
    local roleStyle = RoleService.GetRoleStyle(tagData.Role)
    if roleStyle then
      local billboard = BillboardService.CreateBillboard(player, tagData.Role, roleStyle)
      if billboard then
        tagData.BillboardGUI = billboard
      end
    end

    -- Broadcast to clients
    TagService.BroadcastTagUpdate(player, tagData.Role, roleStyle)
  end
end

-- Handle player leaving
function TagService.HandlePlayerLeaving(player: Player): ()
  debugLog(string.format("Player left: %s", player.Name))

  -- Clean up services
  RoleService.HandlePlayerLeaving(player)
  BillboardService.RemoveBillboard(player)

  -- Clean up tag data
  playerTags[player] = nil
end

-- Assign a tag to a player
function TagService.AssignTag(player: Player): ValidationResult
  local result: ValidationResult = {
    Success = false,
    Role = nil,
    ErrorCode = nil,
    ErrorMessage = nil,
    SecurityFlags = nil,
  }

  -- Validate player
  local playerValidation = Utils.validatePlayer(player)
  if not playerValidation.Success then
    return playerValidation
  end

  -- Get player role
  local role = RoleService.GetPlayerRole(player)
  local roleStyle = RoleService.GetRoleStyle(role)

  if not roleStyle then
    result.ErrorCode = ConfigurationService.GetErrorCodes().INVALID_ROLE
    result.ErrorMessage = string.format("No style found for role: %s", role)
    return result
  end

  -- Check if tag needs updating
  local currentTagData = playerTags[player]
  local needsUpdate = false

  if not currentTagData then
    needsUpdate = true
    playerTags[player] = {
      Role = role,
      BillboardGUI = nil,
      LastUpdated = os.time(),
      ValidationCount = 1,
      SecurityFlags = nil,
    }
  elseif currentTagData.Role ~= role then
    local oldRole = currentTagData.Role
    needsUpdate = true
    currentTagData.Role = role
    currentTagData.LastUpdated = os.time()
    currentTagData.ValidationCount = currentTagData.ValidationCount + 1

    -- Fire tag changed event
    if ConfigurationService.IsEventSystemEnabled() then
      local eventData = EventSystem.CreateEventData(player, role, oldRole, "TagService.AssignTag")
      EventSystem.Fire("TagChanged", eventData)
    end
  end

  if needsUpdate then
    -- Create/update billboard if character exists
    if player.Character and player.Character:FindFirstChild("Head") then
      local billboard = BillboardService.CreateBillboard(player, role, roleStyle)
      if billboard then
        playerTags[player].BillboardGUI = billboard
      end
    end

    -- Broadcast to clients
    TagService.BroadcastTagUpdate(player, role, roleStyle)

    debugLog(string.format("Assigned tag '%s' to player: %s", role, player.Name))
    PerformanceMonitor.RecordTagAssignment(role)
  end

  result.Success = true
  result.Role = role
  return result
end

-- Refresh a player's tag
function TagService.RefreshPlayerTag(player: Player): ValidationResult
  -- Force role re-evaluation
  local role = RoleService.RefreshPlayerRole(player)

  -- Assign the refreshed role
  return TagService.AssignTag(player)
end

-- Handle tag request from client
function TagService.HandleTagRequest(player: Player): ()
  if not Players:GetPlayerByUserId(player.UserId) then
    return
  end

  -- Check rate limit
  local allowed, count = rateLimiter:CheckLimit(player.UserId)
  if not allowed then
    debugLog(string.format("Rate limit exceeded for %s (%d requests)", player.Name, count))
    PerformanceMonitor.RecordSecurityEvent("RATE_LIMIT_EXCEEDED")
    return
  end

  -- Get current tag data
  local tagData = playerTags[player]
  if not tagData then
    TagService.AssignTag(player)
    tagData = playerTags[player]
  end

  if tagData then
    local roleStyle = RoleService.GetRoleStyle(tagData.Role)
    if roleStyle then
      -- Send tag information to the requesting client
      tagUpdateRemote:FireClient(player, player, tagData.Role, roleStyle, roleStyle.Image)
    end
  end
end

-- Broadcast tag update to all clients
function TagService.BroadcastTagUpdate(player: Player, role: string, style: Types.RoleStyle): ()
  if tagUpdateRemote then
    tagUpdateRemote:FireAllClients(player, role, style, style.Image)
    debugLog(string.format("Broadcasted tag update for %s: %s", player.Name, role))
  end
end

-- Start monitoring group rank changes for a player
function TagService.StartGroupRankMonitoring(player: Player): ()
  task.spawn(function()
    local config = ConfigurationService.GetConfiguration()
    local groupId = config.Groups.Primary.Id
    local checkInterval = config.Settings.GroupRankCheckInterval

    local lastKnownRank = player:GetRankInGroup(groupId)

    while player.Parent == Players do
      task.wait(checkInterval)

      local currentRank = player:GetRankInGroup(groupId)
      if currentRank ~= lastKnownRank then
        lastKnownRank = currentRank
        debugLog(string.format("%s's group rank changed to %d", player.Name, currentRank))
        TagService.RefreshPlayerTag(player)
      end
    end
  end)
end

-- Get player tag data
function TagService.GetPlayerTagData(player: Player): PlayerTagData?
  return playerTags[player]
end

-- Get all player tags
function TagService.GetAllPlayerTags(): { [Player]: PlayerTagData }
  return playerTags
end

-- Get tag statistics
function TagService.GetTagStatistics(): { [string]: any }
  local stats = {
    totalPlayers = 0,
    roleDistribution = {},
  }

  for player, tagData in pairs(playerTags) do
    if player.Parent then
      stats.totalPlayers = stats.totalPlayers + 1
      stats.roleDistribution[tagData.Role] = (stats.roleDistribution[tagData.Role] or 0) + 1
    end
  end

  return stats
end

-- Save special tag to DataStore
function TagService.SaveSpecialTag(userId: number, tag: string): boolean
  if not specialTagsStore then
    return false
  end

  local success, err = pcall(function()
    specialTagsStore:SetAsync(tostring(userId), tag)
  end)

  if not success then
    warn(string.format("TBRDS: Failed to save tag for %d: %s", userId, tostring(err)))
    PerformanceMonitor.RecordError("DATASTORE_SAVE_ERROR")
  end

  return success
end

-- Load special tag from DataStore
function TagService.LoadSpecialTag(userId: number): string?
  if not specialTagsStore then
    return nil
  end

  local success, result = pcall(function()
    return specialTagsStore:GetAsync(tostring(userId))
  end)

  if success then
    return result
  else
    warn(string.format("TBRDS: Failed to load tag for %d: %s", userId, tostring(result)))
    PerformanceMonitor.RecordError("DATASTORE_LOAD_ERROR")
    return nil
  end
end

-- Get service status
function TagService.GetServiceStatus(): { [string]: any }
  local stats = TagService.GetTagStatistics()

  return {
    initialized = isInitialized,
    totalPlayers = stats.totalPlayers,
    roleDistribution = stats.roleDistribution,
    rateLimiterActive = rateLimiter ~= nil,
    dataStoreActive = specialTagsStore ~= nil,
    remoteEventsActive = tagUpdateRemote ~= nil and tagRequestRemote ~= nil,
  }
end

-- Cleanup service
function TagService.Cleanup(): ()
  playerTags = {}
  isInitialized = false
  debugLog("Tag service cleaned up")
end

return TagService
